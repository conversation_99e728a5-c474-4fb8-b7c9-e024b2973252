/**
* Author: <PERSON>
* Email: wudong<PERSON>@rongma.com
* Date: 2024/5/20
* Time: 11:33
* Software: GoLand
 */

package errors

import "rm.git/client_api/rm_common_libs.git/v2/library/errors"

var (
	MongoQueryEmpty        = errors.New(10001, "Mongo Query data is empty")
	InvalidParameter       = errors.New(10002, "invalid parameter")
	MongoInsertErr         = errors.New(10003, "Mongo Insert data error")
	MongoUpdateErr         = errors.New(10004, "Mongo Update data error")
	UpgradeMongoQueryEmpty = errors.New(10005, "Mongo Query data error ")
	MinioBucketExists      = errors.New(10006, "Minio Bucket is not exist")
	MinioUploadFileErr     = errors.New(10007, "Minio upload file error")
)
