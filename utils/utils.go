package utils

import (
	"bytes"
	"compress/zlib"
	"encoding/json"
	"fmt"
	"io"
	"strconv"
	"strings"

	"rm.git/client_api/rm_common_libs.git/v2/library/cipher"
	"rm.git/client_api/rm_common_libs.git/v2/library/log"
	"rm.git/client_api/rm_common_libs.git/v2/library/utils"

	"rongma.com/srms/models"
	"rongma.com/srms/modules/consts"
)

func FileSecurityFieldCompatible(modelRequest *models.FileSecurityQueryRequest) *models.FileSecurityQueryRequest {

	if modelRequest.SMD5 != "" {
		modelRequest.MD5 = modelRequest.SMD5
	}
	if modelRequest.SSHA1 != "" {
		modelRequest.SHA1 = modelRequest.SSHA1
	}
	if modelRequest.SSHA256 != "" {
		modelRequest.SHA256 = modelRequest.SSHA256
	}
	if modelRequest.SCommandLineFile != "" {
		modelRequest.CommandLineFile = modelRequest.SCommandLineFile
	}
	if modelRequest.SCommandLineFileMd5 != "" {
		modelRequest.CommandLineFileMd5 = modelRequest.SCommandLineFileMd5
	}
	if modelRequest.SCommandLineFileSha1 != "" {
		modelRequest.CommandLineFileSha1 = modelRequest.SCommandLineFileSha1
	}
	if modelRequest.SCommandLineFileSha256 != "" {
		modelRequest.CommandLineFileSha256 = modelRequest.SCommandLineFileSha256
	}
	return modelRequest
}

func GetFileName(requestData map[string]string) string {
	name := ""
	if requestData["s_operation"] == "createprocess" || requestData["s_operation"] == "queryprocess" {
		name = ParseName(requestData["s_newprocess"])
	} else if requestData["operation"] == "createprocess" || requestData["operation"] == "queryprocess" {
		name = ParseName(requestData["newprocess"])
	} else if requestData["s_operation"] == "loadimage" {
		name = ParseName(requestData["s_newimage"])
	} else if requestData["operation"] == "loadimage" {
		name = ParseName(requestData["newimage"])
	} else if requestData["s_operation"] == "writecomplete" {
		name = ParseName(requestData["s_file"])
	} else if requestData["operation"] == "writecomplete" {
		name = ParseName(requestData["file"])
	} else {
		newName := ParseName(requestData["s_process"])
		if newName != "" {
			name = newName
		} else {
			name = ParseName(requestData["process"])
		}
	}

	if name == "" {
		log.Infof("malice file name is empty, data: %+v", requestData)
	}
	return name
}
func ParseName(nameString string) string {
	if strings.Contains(nameString, `\`) {
		nameSlice := strings.Split(nameString, `\`)
		return nameSlice[len(nameSlice)-1]
	} else if strings.Contains(nameString, `/`) {
		nameSlice := strings.Split(nameString, `/`)
		return nameSlice[len(nameSlice)-1]
	}
	return nameString
}

func ZlibUnCompress(compressSrc string) ([]byte, error) {
	b := bytes.NewReader([]byte(compressSrc))
	var out bytes.Buffer
	r, err := zlib.NewReader(b)
	if err != nil {
		return nil, err
	}
	_, _ = io.Copy(&out, r)
	return out.Bytes(), nil
}

func IntInArray(array []int, i int) bool {
	if len(array) == 0 {
		return false
	}
	for _, value := range array {
		if i == value {
			return true
		}
	}
	return false
}

func FixSecurityResponseData(securityRes *models.FileSecurityQueryResponse, queryOut map[string]interface{}) *models.FileSecurityQueryResponse {
	if v, ok := queryOut["level"]; ok {
		lev, _ := strconv.Atoi(fmt.Sprintf("%v", v))

		if lev > 0 {
			securityRes.Level = lev
			securityRes.LevelSource = consts.LevelSourceEngineQuery
		}
	}

	if val, ok := queryOut["extend"]; ok {
		ext := map[string]interface{}{}

		_ = json.Unmarshal([]byte(fmt.Sprintf("%v", val)), &ext)

		if len(ext) > 0 {
			//Extend无值直接赋值，有值合并
			if len(securityRes.Extend) == 0 {
				securityRes.Extend = ext
			} else {
				for k, v := range ext {
					securityRes.Extend[k] = v
				}
			}
		}
	}

	return securityRes
}

type TokenStruct struct {
	ClientID string `json:"client_id"`
	OrgName  string `json:"org_name"`
	Content  string `json:"content"`
}

// GetTokenStruct 获取 token 结构体
func GetTokenStruct(token string) (TokenStruct, error) {
	tokenStruct := TokenStruct{}

	rmTokenStr := token

	rmTokenStrs1 := strings.Split(rmTokenStr, "-")

	if len(rmTokenStrs1) < 4 {
		tokenDecrypt, err := utils.Base64Decode(token)

		if err != nil {
			return tokenStruct, fmt.Errorf("token Base64Decode 失败：%v", err)
		}

		suite, err := cipher.GetSuite("TLS_RSA_2048_WITH_AES_128_ECB_SHA256")

		if err != nil {
			return tokenStruct, fmt.Errorf("GetSuite 失败：%v", err)
		}

		rmTokenDecrypt, err := suite.Rsa.PublicDecrypt(tokenDecrypt)

		if err != nil {
			return tokenStruct, fmt.Errorf("PublicDecrypt 失败：%v", err)
		}

		rmTokenStr = string(rmTokenDecrypt)
		rmTokenStrs1 = strings.Split(rmTokenStr, "-")
	}

	// 分解token
	rmTokenStrs2 := strings.Split(rmTokenStr, "#")

	tokenStruct.ClientID = rmTokenStrs1[0]

	tokenStruct.OrgName = "rm.tech"
	if len(rmTokenStrs2) > 1 {
		tokenStruct.OrgName = rmTokenStrs2[1]
	}

	tokenStruct.Content = rmTokenStr

	return tokenStruct, nil
}
