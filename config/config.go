package config

import (
	"fmt"
	"os"

	"github.com/spf13/viper"
	"rm.git/client_api/rm_common_libs.git/v2/common/configs"
)

var (
	v         = viper.New()
	appConfig = NewAppConfig()

	MongoDBSrms                   = "srms"
	MongoDBEngines                = "engines"
	MongodbInstruction            = "client_instructions"
	MinioSrmsShardBucket          = "sample-file-shards"
	KafkaReadScanTaskResultsTopic = "scan-task-results" // 用于病毒扫描
)

type ServiceConfig struct { //nolint:maligned
	Addr                   string
	Name                   string
	Mode                   string
	IsSaas                 bool   `mapstructure:"is_saas"`
	CcsName                string `mapstructure:"ccs_name"`
	UploadSwitch           string `mapstructure:"upload_switch"`
	FileQueryRank          bool   `mapstructure:"file_query_rank"`
	CheckLevel             []int  `mapstructure:"check_level"`
	RequestLengthThreshold int    `mapstructure:"request_length_threshold"` // 请求体长度阈值
	IgnoreIsFoundQax       bool   `mapstructure:"ignore_isfound_qax"`       //是否忽略qax返回的是否找到level等级
	ShardSize              int64  `mapstructure:"shard_size"`
	ThresholdLimit         int64  `mapstructure:"threshold_limit"`
	ThresholdMinute        int64  `mapstructure:"threshold_minute"`
}

type IsolateType struct {
	FileCount int `json:"file_count" mapstructure:"file_count"`
}

type UploadConfig struct {
	Quantity        int
	IntervalTime    int `mapstructure:"interval_time"`
	APIIntervalTime int `mapstructure:"api_interval_time"`
}

type TokenBucket struct {
	Rate     float64 `mapstructure:"rate"`
	Capacity float64 `mapstructure:"capacity"`
}

type AppConfig struct {
	Service         ServiceConfig
	Certs           configs.CertsConfig    `mapstructure:"certs"`
	Redis           configs.RedisConfig    `mapstructure:"redis_default"`
	Logging         map[string]interface{} `mapstructure:"logging"`
	GrpcFile        configs.GRPCConfig     `mapstructure:"grpc_file"`
	GrpcSrms        configs.GRPCConfig     `mapstructure:"grpc_srms"`
	GrpcInfo        configs.GRPCConfig     `mapstructure:"grpc_info"`
	GrpcEngineQuery configs.GRPCConfig     `mapstructure:"grpc_engine_query"`
	GrpcPortal      configs.GRPCConfig     `mapstructure:"grpc_portal"`
	Minio           configs.MinioConfig    `mapstructure:"minio_default"`
	Mongodb         configs.MongoDBConfig  `mapstructure:"mongodb_default"`
	MongodbEngines  configs.MongoDBConfig  `mapstructure:"mongodb_engine"`
	GrpcGroup       configs.GRPCConfig     `mapstructure:"grpc_group"`
	ActLogger       configs.ActLoggerConfig
	Uploads         UploadConfig
	TokenBucket     TokenBucket         `mapstructure:"token_bucket"`
	Kafka           configs.KafkaConfig `mapstructure:"kafka_default"`
}

func NewAppConfig() *AppConfig {
	return &AppConfig{}
}

func init() {
	v.AutomaticEnv()
	v.SetConfigType("toml")
	v.AddConfigPath("conf")

	// 加载公共配置
	v.SetConfigName("common")

	if err := v.ReadInConfig(); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：load common config failed，", err, "\033[0m")
		os.Exit(-1)
	}

	if err := v.Unmarshal(&appConfig); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：load common config failed，", err, "\033[0m")
		os.Exit(-1)
	}

	// 加载业务配置
	v.SetConfigName("config")

	if err := v.ReadInConfig(); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：load config failed，", err, "\033[0m")
		os.Exit(-1)
	}

	if err := v.Unmarshal(&appConfig); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：load config failed，", err, "\033[0m")
		os.Exit(-1)
	}
}

func Config() *AppConfig {
	return appConfig
}
