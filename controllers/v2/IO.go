package v2

import (
	"bytes"
	"compress/zlib"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"rongma.com/srms/modules/logger"

	"github.com/gin-gonic/gin"
	"rm.git/client_api/rm_common_libs.git/v2/common/clients/actlogger/logstruct"
	"rm.git/client_api/rm_common_libs.git/v2/common/common"
	"rm.git/client_api/rm_common_libs.git/v2/common/misc"
	"rm.git/client_api/rm_common_libs.git/v2/common/protobuf/portal"
	"rm.git/client_api/rm_common_libs.git/v2/library/cipher"
	"rm.git/client_api/rm_common_libs.git/v2/library/errors"
	"rm.git/client_api/rm_common_libs.git/v2/library/utils"

	"rongma.com/srms/models"
	"rongma.com/srms/modules/consts"

	"rongma.com/srms/config"
	"rongma.com/srms/modules/client"
)

// RMToken 定义基础接收数据格式
type RMToken struct {
	ClientID string
	Content  string
	OrgName  string
}

const (
	rmToken   = "rm-token"
	rmSession = "rm-session"
	channel   = "channel"
)

type RMSession struct {
}

type RMRequestData struct {
	Data    map[string]string
	Token   *common.TokenStruct
	Session *RMSession
}

var (
	conf = config.Config()
)

func parseSession() (*RMSession, error) {
	ts := RMSession{}
	return &ts, nil
}

/**
 * 连接channel,获取加密key信息
 */
func getChannelAcquire(ctx context.Context, channel string, resp *portal.AcquireChannelResponse) error {
	var (
		acq             = models.AcquireChannel{}
		channelCacheKey = consts.GetChannelCacheKey(channel)
	)

	// 先从redis中获取，获取失败再连线获取
	info, err := client.Redis.Get(ctx, channelCacheKey).Result()
	if err == nil && len(info) > 0 {
		if err := json.Unmarshal([]byte(info), &acq); err == nil {
			resp.SecretKey = acq.SecretKey
			resp.CipherSuite = acq.CipherSuite
		}
		return nil
	}

	res, err := client.Portal.AcquireChannel(ctx, &portal.AcquireChannelRequest{Channel: channel})
	if err != nil {
		logger.WithContext(ctx).Errorf("[channel acquire] request portal err: %v,channel: %s", err, channel)
		return errors.InvalidChannel
	}

	resp.SecretKey = res.GetSecretKey()
	resp.CipherSuite = res.GetCipherSuite()

	//存入redis缓存
	acq.SecretKey = res.GetSecretKey()
	acq.CipherSuite = res.GetCipherSuite()
	value, err := json.Marshal(acq)
	if err != nil {
		logger.WithContext(ctx).Errorf("[channel acquire] cache secret err: %v,channel: %s", err, channel)
		//return err
	}
	_, err = client.Redis.Set(ctx, channelCacheKey, string(value), 5*time.Minute).Result()
	if err != nil {
		logger.WithContext(ctx).Errorf("[channel acquire] cache secret err: %v, channel: %s", err, channel)
		//return err
	}

	return nil
}

/**
 * 加密返回结果
 */
func encryptResponseMessage(data []byte, encryptKey string) (string, error) {
	var err error
	secretKey, err := utils.Base64Decode(encryptKey)
	if err != nil {
		return "", err
	}
	suite, _ := cipher.GetSuite(cipher.TLS_RSA_2048_WITH_AES_128_ECB_SHA256)
	encryptResult, err := suite.Aes.Encrypt(data, secretKey, nil)
	if err != nil {
		return "", err
	}

	return string(encryptResult), nil
}

/**
 * 检查请求体内容
 */
func readBodyContent(ctx *gin.Context) ([]byte, error) {

	var content []byte
	var err error

	if ctx.Request.ContentLength == -1 {
		return nil, errors.ContentLengthRequired
	}

	if ctx.Request.ContentLength == 0 {
		return content, nil
	}

	if content, err = io.ReadAll(ctx.Request.Body); err != nil {
		return nil, err
	}

	if (int64)(len(content)) != ctx.Request.ContentLength {
		return nil, errors.ContentLengthNotMatch
	}

	return content, nil

}

// 解密channel传输数据内容,如果报错则同时清理redis中对channel密钥的缓存
func decryptChannelMessage(ctx context.Context, data []byte, key string, cipherSuite string, channel string, isCheckString bool) (str string, error error) {
	defer func() {
		if err := recover(); err != nil {
			logger.WithContext(ctx).Errorf("decrypt channel message error, err: %v, length: %d, channel: %s", err, len(data), channel)
			error = errors.DecryptChannelMessage
		}
	}()
	var suite *cipher.Suite

	if suite, error = cipher.GetSuite(cipherSuite); error != nil {
		logger.WithContext(ctx).Errorf("GetSuite err: %s", error)
		return "", error
	}

	secretKey, err := utils.Base64Decode(key)
	if err != nil {
		logger.WithContext(ctx).Errorf("Base64Decode err: %s", error)
		return "", err
	}
	decrypted, err := suite.Aes.Decrypt(data, secretKey, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("Decrypt err: %s", err)
		return "", errors.ContentFormatNotMatch
	}

	dec := string(decrypted)
	if isCheckString {
		if !utf8.ValidString(dec) {
			logger.WithContext(ctx).Errorf("decrypt body is invalid string, data: %s, channel: %s", dec, channel)
			return "", errors.ChannelAcquireDecodeErr
		}
	}
	return strings.Trim(dec, "\r\n"), nil
}

// 压缩字符内容
/*func zlibContent(src []byte) []byte {

	var in bytes.Buffer
	w := zlib.NewWriter(&in)
	_, _ = w.Write(src)
	_ = w.Close()
	return in.Bytes()
}*/

// 解压缩字符内容
func unZlibContent(compressSrc []byte) []byte {
	b := bytes.NewReader(compressSrc)
	var out bytes.Buffer
	r, _ := zlib.NewReader(b)
	_, _ = io.Copy(&out, r)
	return out.Bytes()
}

func actErrorLog(ctx *gin.Context, code int, res string) {
	var err logstruct.Error

	clientId := ctx.GetString("client_id")
	token := ctx.GetString("ACTLOG_TOKEN")
	orgName := ctx.GetString("ACTLOG_ORG_NAME")
	clientIp := common.GetClientIp(ctx)
	request := ctx.GetString("ACTLOG_REQUEST")
	if len(request) > conf.Service.RequestLengthThreshold {
		request = fmt.Sprintf("request body too long(> %d)", conf.Service.RequestLengthThreshold)
	}

	headerJson := ""
	if headerJsonBytes, e := json.Marshal(ctx.Request.Header); e != nil {
		logger.WithContext(ctx).Errorf("request header marshal error: %v, header info: %v", e, ctx.Request.Header)
	} else {
		headerJson = string(headerJsonBytes)
	}

	err = logstruct.Error{
		UrlPach:   ctx.Request.RequestURI,
		Method:    ctx.Request.Method,
		Header:    headerJson,
		Request:   request,
		ErrorCode: strconv.Itoa(code),
		Message:   res,
	}
	if conf.ActLogger.ActlogIndexName == "" {
		logger.WithContext(ctx).Errorf("error call actLogger config: %+v", conf.ActLogger)
	}
	client.ActLogger.Error(conf.Service.Name, ctx, err, clientId, clientIp, token, orgName, conf.ActLogger.ActlogIndexName, 1)
}

func actAccessLog(ctx *gin.Context, response string) {
	clientId := ctx.GetString("client_id")
	token := ctx.GetString("ACTLOG_TOKEN")
	orgName := ctx.GetString("ACTLOG_ORG_NAME")
	clientIp := common.GetClientIp(ctx)
	request := ctx.GetString("ACTLOG_REQUEST")
	if len(request) > conf.Service.RequestLengthThreshold {
		request = fmt.Sprintf("request body too long( > %d bytes)", conf.Service.RequestLengthThreshold)
	}
	access := logstruct.Access{
		UrlPach:  ctx.Request.RequestURI,
		Method:   ctx.Request.Method,
		Request:  request,
		Header:   ctx.GetString("ACTLOG_HEADER"),
		Response: response,
	}

	if conf.ActLogger.ActlogIndexName == "" {
		logger.WithContext(ctx).Errorf("access call actLogger config: %+v", conf.ActLogger)
	}
	client.ActLogger.Access(conf.Service.Name, ctx, access, clientId, clientIp, token, orgName, conf.ActLogger.ActlogIndexName, 1)
}

func getToken(ctx *gin.Context) ([]byte, error) {
	headerToken := ctx.GetHeader(rmToken)
	logger.WithContext(ctx).Infof("Token 1: %s", headerToken)
	if headerToken == "" {
		return nil, errors.InvalidParameter
	}
	decode, err := utils.Base64Decode(headerToken)
	if err != nil {
		return nil, err
	}
	return decode, nil
}

func GetSession(ctx *gin.Context, secretKey string, cipherSuite string, channel string) (string, error) {
	var (
		sessionString string
		headerSession = ctx.GetHeader(rmSession)
	)

	logger.WithContext(ctx).Infof("Session 1: %s", headerSession)
	if len(headerSession) == 0 {
		return sessionString, errors.New(109, "rm-session is empty")
	}

	headerDecode, err := utils.Base64Decode(headerSession)
	if err != nil {
		return "", err
	}

	logger.WithContext(ctx).Infof("Session 2: %s", string(headerDecode))
	sessionString, err = decryptChannelMessage(ctx, headerDecode, secretKey, cipherSuite, channel, true)
	if err != nil {
		return "", err
	}

	logger.WithContext(ctx).Infof("Session 3: %s", "session finish")
	return sessionString, nil
}

// IORequest 请求数据流处理, parseBody = true时 解析body参数，及输出请求日志
func IORequest(ctx *gin.Context, channelAcq *portal.AcquireChannelResponse, parseBody bool, subType string) (*RMRequestData, error) {
	var (
		requestData = &RMRequestData{}
		actHeader   = make(map[string]string, 2)
		channelId   = ctx.Query(channel)
		orgData     string
	)

	if len(channelId) == 0 {
		logger.WithContext(ctx).Errorf("[IORequest] channel is empty")
		return nil, errors.ChannelAcquireErrTo500
	}

	// 1.调用channel获取加密信息
	err := getChannelAcquire(ctx, channelId, channelAcq)
	if err != nil {
		logger.WithContext(ctx).Errorf("[IORequest] get channel acquire error: %s, change code 500", err)
		return nil, errors.ChannelAcquireErrTo500
	}

	// 2.解析token
	token, err := getToken(ctx)
	if err != nil {
		logger.WithContext(ctx).Errorf("[IORequest] get token err: %s", err)
		return nil, err
	}
	//解密token,如果失败重新获取一下密钥
	tokenString, err := decryptChannelMessage(ctx, token, channelAcq.SecretKey, channelAcq.CipherSuite, channelId, true)
	if err != nil {
		channelCacheKey := consts.GetChannelCacheKey(channelId)
		client.Redis.Del(context.TODO(), channelCacheKey)
		logger.WithContext(ctx).Errorf("[IORequest] clear channel cache: %s", channelCacheKey)

		if err = getChannelAcquire(ctx, channelId, channelAcq); err != nil { //重新获取一下密钥
			logger.WithContext(ctx).Errorf("[IORequest] get channel acquire again error: %s, change code 500", err)
			return nil, errors.ChannelAcquireErrTo500
		} else {
			//再次尝试解密
			tokenString, err = decryptChannelMessage(ctx, token, channelAcq.SecretKey, channelAcq.CipherSuite, channelId, true)
			if err != nil {
				logger.WithContext(ctx).Errorf("[IORequest] decrypt token again error: %s", err)
				return nil, errors.ChannelAcquireErrTo500
			}
		}
	}

	actHeader["rm-token"] = tokenString
	tokenStruct, err := common.GetTokenStruct(tokenString)
	if err != nil {
		logger.WithContext(ctx).Errorf("[IORequest] parse token err: %s", err)
		return nil, errors.ChannelAcquireErrTo500
	}

	requestData.Token = &tokenStruct

	logger.WithContext(ctx).Infof("Token : %+v", requestData.Token)
	ctx.Set("ACTLOG_ORG_NAME", requestData.Token.OrgName)

	// 3.解析session
	sessionString, err := GetSession(ctx, channelAcq.SecretKey, channelAcq.CipherSuite, channelId)
	if err == nil {
		actHeader["rm-session"] = sessionString
		requestData.Session, err = parseSession()
		if err != nil {
			logger.WithContext(ctx).Errorf("[IORequest] parse session err: %s", err)
			return nil, err
		}
	}
	logger.WithContext(ctx).Infof("Session : %+v", requestData.Session)

	// 4.解密request的data数据
	bodyContent, err := readBodyContent(ctx) // 检验内容是否符合,返回元数据二进制
	if err != nil {
		logger.WithContext(ctx).Errorf("[IORequest] read body content err: %s", err)
		return nil, err
	}

	if bodyContent != nil {
		// 5.解密数据内容
		bodyContentData, err := decryptChannelMessage(ctx, bodyContent, channelAcq.SecretKey, channelAcq.CipherSuite, channelId, false)
		if err != nil {
			logger.WithContext(ctx).Errorf("[IORequest] body decryptChannelMessage err: %s", err)
			return nil, err
		}
		orgData = bodyContentData
		// 6.读取内容体，如果请求包含压缩信息，进行解压后再处理
		if ctx.GetHeader("Content-Type") == "application/x-zip-compressed" {
			orgData = string(unZlibContent([]byte(bodyContentData)))
		}

		paramsArr := make(map[string]string)
		if parseBody {
			params := strings.Split(orgData, subType)
			for _, param := range params {
				key, value, _ := strings.Cut(param, "=")
				if key != "" {
					lowerKey := strings.ToLower(key)
					if lowerKey == "b64_signerexex" {
						if value != "" {
							if val, err := base64.StdEncoding.DecodeString(value); err != nil {
								logger.WithContext(ctx).Errorf("[IORequest] %s base64 decode string error: %s", value, err)
							} else {
								paramsArr[lowerKey] = strings.ReplaceAll(strings.ToLower(string(val)), "\n", "")
							}
						}
					} else if lowerKey == "file_path" {
						paramsArr[lowerKey] = strings.ReplaceAll(value, "\n", "")
					} else {
						paramsArr[lowerKey] = strings.ReplaceAll(strings.ToLower(value), "\n", "")
					}
				}
			}
		} else {
			paramsArr["body"] = orgData
		}
		requestData.Data = paramsArr
	}

	// 7.记录log
	if parseBody {
		ctx.Set("ACTLOG_REQUEST", orgData)
	} else {
		ctx.Set("ACTLOG_REQUEST", "")
	}
	ctx.Set("ACTLOG_TOKEN", tokenString)
	ctx.Set("ACTLOG_SESSION", sessionString)

	actHeaderString, err := json.Marshal(actHeader)
	if err == nil {
		ctx.Set("ACTLOG_HEADER", string(actHeaderString))
	}

	return requestData, nil
}

// IOResponse 返回数据流处理
func IOResponse(ctx *gin.Context, res interface{}, channelAcq *portal.AcquireChannelResponse) {

	response := misc.NewResponse(res).SetSession(ctx.GetString("ACTLOG_SESSION")).SetRequestId(ctx.GetString(consts.HeaderXRequestID))

	jsonEncodeRes, err := json.Marshal(response)
	if err != nil {
		logger.WithContext(ctx).Errorf("Error: response json marshal error : %+v", err)
		return
	}

	if response.Error == http.StatusInternalServerError {

		actLog(ctx, response.Error, jsonEncodeRes)

		ctx.Header("Content-Type", "text/plain")
		ctx.String(http.StatusInternalServerError, "500")
	} else {
		ResponseRes, err := encryptResponseMessage(jsonEncodeRes, channelAcq.SecretKey)
		if err != nil {
			logger.WithContext(ctx).Errorf("Error: response encrypt error : %+v", err)
			return
		}

		actLog(ctx, response.Error, jsonEncodeRes)

		ctx.Header("Content-Type", "text/plain")
		ctx.String(http.StatusOK, ResponseRes)
	}

}

// logger.WithContext(ctx) 打印
func actLog(ctx *gin.Context, code int, jsonEncodeRes []byte) {
	if code == 0 {
		actAccessLog(ctx, string(jsonEncodeRes))
	} else {
		actErrorLog(ctx, code, string(jsonEncodeRes))
	}
}

func IOResponseUnEncode(ctx *gin.Context, res interface{}) {

	response := misc.NewResponse(res).SetSession(ctx.GetString("ACTLOG_SESSION")).SetRequestId(ctx.GetString(consts.HeaderXRequestID))
	response.Data = utils.Base64Encode(utils.BytesEncode(response.Data))

	jsonEncodeRes, err := json.Marshal(response)
	if err != nil {
		logger.WithContext(ctx).Errorf("Error: response json marshal error : %+v", err)
		return
	}

	if response.Error == http.StatusInternalServerError {
		actLog(ctx, response.Error, jsonEncodeRes)
	} else {
		actLog(ctx, response.Error, jsonEncodeRes)
	}
	ctx.Header("Content-Type", "application/json")
	ctx.IndentedJSON(http.StatusOK, response)
}
