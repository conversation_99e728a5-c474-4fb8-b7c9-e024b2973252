package v2

import (
	"bytes"
	"compress/zlib"
	"context"
	"encoding/json"
	"fmt"
	"io"

	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
	"rm.git/client_api/rm_common_libs.git/v2/common/common"
	"rm.git/client_api/rm_common_libs.git/v2/common/protobuf/portal"
	"rm.git/client_api/rm_common_libs.git/v2/library/errors"

	"rongma.com/srms/controllers"
	"rongma.com/srms/models"
	"rongma.com/srms/modules/client"
	"rongma.com/srms/modules/consts"
	"rongma.com/srms/modules/logger"
	"rongma.com/srms/rmgrpc/rmmd5"
	"rongma.com/srms/services"
	"rongma.com/srms/utils"
)

type SRMSControllerV2 struct {
	service     services.ISRMSService
	isolateFile services.IIsolateFileService
}

func NewSRMSControllerV2(service services.ISRMSService, isolate services.IIsolateFileService) *SRMSControllerV2 {
	return &SRMSControllerV2{service: service, isolateFile: isolate}
}

// OnBatchQueryFileSecurity 批量获取文件安全等级，参数列表长度最大30
func (s *SRMSControllerV2) OnBatchQueryFileSecurity(ctx *gin.Context) {
	channelAcquire := portal.AcquireChannelResponse{}
	rmRequestData, err := IORequest(ctx, &channelAcquire, false, consts.SubType1)
	if err != nil {
		logger.WithContext(ctx).Errorf("[batch_query] io request err: %s", err)
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	clientId := rmRequestData.Token.ClientID
	channelId := ctx.Query("channel")
	orgName := rmRequestData.Token.OrgName
	logger.WithContext(ctx).Infof("[batch_query] client_id: %s, channelId: %s, orgName: %s", clientId, channelId, orgName)

	ctx.Set("client_id", clientId)
	body, ok := rmRequestData.Data["body"]
	if !ok {
		logger.WithContext(ctx).Errorf("[batch_query] body is null, client_id: %s, channelId: %s, orgName: %s", clientId, channelId, orgName)
		IOResponse(ctx, errors.InvalidParameter, &channelAcquire)
		return
	}

	modelRequest := &models.FileSecurityBatchQueryRequest{}
	err = json.Unmarshal([]byte(body), &modelRequest)
	if err != nil {
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	if len(modelRequest.QueryList) > consts.MaxQueryListLength {
		IOResponse(ctx, errors.New(230, fmt.Sprintf("query list size too long, max size %d", consts.MaxQueryListLength)), &channelAcquire)
		return
	}
	resBaseParamList := make([]models.BatchQueryBaseParam, 0, len(modelRequest.QueryList))

	batchRequest := &rmmd5.BatchGetMd5InfoRequest{}
	for _, v := range modelRequest.QueryList {
		batchRequest.RequestCon = append(batchRequest.RequestCon, &rmmd5.RequestCon{
			Md5:  v.Md5,
			Sha1: v.Sha1,
		})
		resBaseParamList = append(resBaseParamList, models.BatchQueryBaseParam{Md5: v.Md5, Sha1: v.Sha1})
	}

	batchInfo, err := client.RmMd5.BatchGetMd5Info(context.Background(), batchRequest)
	if err != nil {
		logger.WithContext(ctx).Errorf("[batch_query] grpc found md5 info error: %s, query list: %+v ", err, modelRequest.QueryList)
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	if batchInfo.GetData() == nil {
		logger.WithContext(ctx).Errorf("[batch_query] grpc found md5 info error, data is null query list: %+v", modelRequest.QueryList)
		IOResponse(ctx, errors.InvalidRequest, &channelAcquire)
		return
	}

	for i, v := range batchInfo.GetData() {
		resBaseParamList[i].Level = int(v.Security.Level)
	}

	res := make(map[string]interface{}, 1)
	res["query_result"] = resBaseParamList
	IOResponse(ctx, res, &channelAcquire)
}

// OnQueryFileSecurity 样本文件查询
func (s *SRMSControllerV2) OnQueryFileSecurity(ctx *gin.Context) {

	channelAcquire := portal.AcquireChannelResponse{}
	rmRequestData, err := IORequest(ctx, &channelAcquire, true, consts.SubType1)
	if err != nil {
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	ctx.Set("client_id", rmRequestData.Token.ClientID)

	level, err := s.isolateFile.GetCustomLevel(ctx, rmRequestData.Token.OrgName, rmRequestData.Data["s_sha1"], rmRequestData.Token.ClientID)
	if err == nil && level != 0 {
		//如果存在 那么就返回，如果不存在就不返回
		resp := &models.FileSecurityQueryResponse{MD5: rmRequestData.Data["s_md5"], Level: level, NeedUpload: 0, SHA1: rmRequestData.Data["s_sha1"]}
		IOResponse(ctx, resp, &channelAcquire)
		return
	}

	resp, err := s.service.OnQueryFileSecurity(ctx, rmRequestData.Data, &models.RequestAuth{
		ClientID: rmRequestData.Token.ClientID,
		ClientIP: common.GetClientIp(ctx),
		Channel:  ctx.Query("channel"),
		Token:    ctx.GetString("ACTLOG_TOKEN"),
		OrgName:  rmRequestData.Token.OrgName,
	})

	if err != nil {
		IOResponse(ctx, err, &channelAcquire)
		return
	}
	// 开关关闭，无论如何都不能上传
	if conf.Service.UploadSwitch != "on" {
		resp.NeedUpload = 0
	}

	if cloudQueryOut, err := controllers.GetCloudQueryLevel(ctx, rmRequestData.Data, rmRequestData.Token.ClientID); err == nil {
		resp = utils.FixSecurityResponseData(resp, cloudQueryOut)
	}

	IOResponse(ctx, resp, &channelAcquire)
}

// OnUpload 样本文件上传请求
func (s *SRMSControllerV2) OnUpload(ctx *gin.Context) {
	channelAcquire := portal.AcquireChannelResponse{}
	rmRequestData, err := IORequest(ctx, &channelAcquire, true, consts.SubType1)
	if err != nil {
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	resp, err := s.service.OnUpload(ctx, rmRequestData.Data, &models.RequestAuth{
		ClientID: rmRequestData.Token.ClientID,
		ClientIP: common.GetClientIp(ctx),
		Channel:  ctx.Query("channel"),
		Token:    ctx.GetString("ACTLOG_TOKEN"),
		OrgName:  rmRequestData.Token.OrgName,
	})
	ctx.Set("client_id", rmRequestData.Token.ClientID)

	if err != nil {
		IOResponse(ctx, err, &channelAcquire)
		return
	}
	IOResponse(ctx, resp, &channelAcquire)

}

// OnUploadShard 样本文件上传
func (s *SRMSControllerV2) OnUploadShard(ctx *gin.Context) {
	channelAcquire := portal.AcquireChannelResponse{}
	rmRequestData, err := IORequest(ctx, &channelAcquire, false, consts.SubType1)
	if err != nil {
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	body, ok := rmRequestData.Data["body"]
	if !ok {
		IOResponse(ctx, errors.InvalidParameter, &channelAcquire)
		return
	}

	fileToken := ctx.Query("file_token")
	if fileToken == "" {
		IOResponse(ctx, errors.InvalidParameter, &channelAcquire)
		return
	}

	resp, err := s.service.OnUploadShard(ctx, body, fileToken, &models.RequestAuth{
		ClientID: rmRequestData.Token.ClientID,
		ClientIP: common.GetClientIp(ctx),
		Channel:  ctx.Query("channel"),
		Token:    ctx.GetString("ACTLOG_TOKEN"),
		OrgName:  rmRequestData.Token.OrgName,
	})
	ctx.Set("client_id", rmRequestData.Token.ClientID)

	if err != nil {
		IOResponse(ctx, err, &channelAcquire)
		return
	}
	IOResponse(ctx, resp, &channelAcquire)

}

func (s *SRMSControllerV2) OnDumpUpload(ctx *gin.Context) {
	channelAcquire := portal.AcquireChannelResponse{}
	rmRequestData, err := IORequest(ctx, &channelAcquire, false, consts.SubType1)
	if err != nil {
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	clientId := rmRequestData.Token.ClientID
	partnerId := ctx.Query("org_name") // todo 合作者id，用于后期可以根据客户来配置上传频率
	fileName := ctx.Query("file_name")

	ctx.Set("client_id", clientId)

	if fileName == "" {
		logger.WithContext(ctx).Errorf("[dump] lost file_name parameter")
		IOResponse(ctx, errors.InvalidParameter, &channelAcquire)
		return
	}

	modelRequest := &models.DumpUploadRequest{}
	modelRequest.OrgName = partnerId
	modelRequest.FileName = fmt.Sprintf("dump_%s_%s_%s.file", partnerId, clientId, fileName)

	parseContentStr, ok := rmRequestData.Data["body"]
	if !ok {
		IOResponse(ctx, errors.InvalidParameter, &channelAcquire)
		return
	}

	// 解密后数据进行解压缩处理
	headerStr := ctx.GetHeader("Content-Type")
	modelRequest.Data = parseContentStr

	if headerStr == "application/x-zip-compressed" {
		bodyContent, err := ZlibUnCompress(parseContentStr)
		if err != nil {
			logger.WithContext(ctx).Errorf("[dump] zlib unCompress error : %v", err)
			IOResponse(ctx, err, &channelAcquire)
			return
		}
		modelRequest.Data = string(bodyContent)
	}

	if len(modelRequest.Data) == 0 || len(modelRequest.FileName) == 0 {
		logger.WithContext(ctx).Errorf("[dump] parameter error, file_name:%s, data length:%d", modelRequest.FileName, len(modelRequest.Data))
		IOResponse(ctx, errors.InvalidParameter, &channelAcquire)
		return
	}
	res := models.DumpUploadResponse{}
	//res, err := s.service.OnDumpUpload(ctx, modelRequest) // 调用上传方法，实现功能逻辑
	//if err != nil {
	//	logger.WithContext(ctx).Errorf("[dump] upload shard error:%s", err)
	//	IOResponse(ctx, err, &channelAcquire)
	//	return
	//}

	IOResponse(ctx, res, &channelAcquire)
}

func (s *SRMSControllerV2) OnGetDumpConfig(ctx *gin.Context) {
	channelAcquire := portal.AcquireChannelResponse{}
	rmRequestData, err := IORequest(ctx, &channelAcquire, true, consts.SubType1)
	if err != nil {
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	ctx.Set("client_id", rmRequestData.Token.ClientID)

	// json解析出请求内容
	modelRequest := &models.DumpConfigRequest{}
	if err = mapstructure.Decode(rmRequestData.Data, modelRequest); err != nil {
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	if len(modelRequest.ProductVer) == 0 {
		logger.WithContext(ctx).Errorf("[query] product version is null")
		IOResponse(ctx, errors.InvalidParameter, &channelAcquire)
		return
	}

	res := &models.DumpConfigResponse{}
	//res, err = s.service.OnGetDumpConfig(ctx, modelRequest) // 调用上传方法，实现功能逻辑
	//if err != nil {
	//	logger.WithContext(ctx).Errorf("[query] query dump config error, %s", err)
	//	IOResponse(ctx, err, &channelAcquire)
	//	return
	//}

	IOResponse(ctx, res, &channelAcquire)
}

func ZlibUnCompress(compressSrc string) ([]byte, error) {
	b := bytes.NewReader([]byte(compressSrc))
	var out bytes.Buffer
	r, err := zlib.NewReader(b)
	if err != nil {
		return nil, err
	}
	_, _ = io.Copy(&out, r)
	return out.Bytes(), nil
}

// OnGetUploadConfig 查询上传文件配置信息
func (s *SRMSControllerV2) OnGetUploadConfig(ctx *gin.Context) {

	cf := conf.Uploads
	res := models.UploadConfigResponse{
		Quantity:        cf.Quantity,
		IntervalTime:    cf.IntervalTime,
		APIIntervalTime: cf.APIIntervalTime,
	}

	channelAcquire := portal.AcquireChannelResponse{}
	rmRequestData, err := IORequest(ctx, &channelAcquire, true, consts.SubType1)
	if err != nil {
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	ctx.Set("client_id", rmRequestData.Token.ClientID)

	IOResponse(ctx, res, &channelAcquire)
}

// OnQuarantineFileInform 保存隔离文件信息
func (s *SRMSControllerV2) OnQuarantineFileInform(ctx *gin.Context) {
	channelAcquire := portal.AcquireChannelResponse{}
	rmRequestData, err := IORequest(ctx, &channelAcquire, false, consts.SubType1)
	if err != nil {
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	clientId := rmRequestData.Token.ClientID
	//orgName := rmRequestData.Token.OrgName

	ctx.Set("client_id", clientId)

	modelRequest := make([]models.QuarantineFileRequest, 0)
	requestBytes := rmRequestData.Data["body"]
	if err = json.Unmarshal([]byte(requestBytes), &modelRequest); err != nil {
		logger.WithContext(ctx).Errorf("json unmarshal err: %v", modelRequest)
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	if len(modelRequest) > conf.Uploads.Quantity {
		IOResponse(ctx, errors.FileCountOutOfConfig, &channelAcquire)
		return
	}
	res := map[string]interface{}{
		"msg": "quarantine file info upload success!",
	}
	//res, err := s.service.OnQuarantineFileInform(ctx, modelRequest, clientId, orgName)
	//if err != nil {
	//	IOResponse(ctx, err, &channelAcquire)
	//	return
	//}

	logger.WithContext(ctx).Infof("[upload quarantine file] result:%+v", res)

	IOResponse(ctx, res, &channelAcquire)
}

func (s *SRMSControllerV2) OnTrustFileInform(ctx *gin.Context) {
	channelAcquire := portal.AcquireChannelResponse{}
	rmRequestData, err := IORequest(ctx, &channelAcquire, false, consts.SubType1)
	if err != nil {
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	clientId := rmRequestData.Token.ClientID
	//orgName := rmRequestData.Token.OrgName
	// channelId := ctx.Query("channel")

	ctx.Set("client_id", clientId)

	modelRequest := make([]models.TrustFileRequest, 0)
	requestBytes := rmRequestData.Data["body"]
	if err = json.Unmarshal([]byte(requestBytes), &modelRequest); err != nil {
		logger.WithContext(ctx).Errorf("json unmarshal err: %v", modelRequest)
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	if len(modelRequest) > conf.Uploads.Quantity {
		IOResponse(ctx, errors.FileCountOutOfConfig, &channelAcquire)
		return
	}
	res := map[string]interface{}{
		"msg": "trust file info upload success!",
	}
	//res, err := s.service.OnTrustFileInform(ctx, modelRequest, clientId, orgName)
	//if err != nil {
	//	IOResponse(ctx, err, &channelAcquire)
	//	return
	//}

	logger.WithContext(ctx).Infof("[upload trust file] result:%+v", res)

	IOResponse(ctx, res, &channelAcquire)
}

func (s *SRMSControllerV2) OnQueryFile(ctx *gin.Context) {
	// 获取token 解除orgName的数据

	rmTokenVlaue := ctx.GetHeader(rmToken)
	if len(rmTokenVlaue) == 0 {
		logger.WithContext(ctx).Warnf("Rm token error: rmToken is empty ")
		IOResponseUnEncode(ctx, errors.InvalidParameter)
		return
	}
	ctx.Set("ACTLOG_TOKEN", rmTokenVlaue)

	tokenStruct, err := utils.GetTokenStruct(rmTokenVlaue)
	if err != nil {
		logger.WithContext(ctx).Warnf("Get Token Struct error: %s", err)
		IOResponseUnEncode(ctx, err)
		return
	}

	ctx.Set("client_id", tokenStruct.ClientID)
	ctx.Set("org_name", tokenStruct.OrgName)
	ctx.Set("ACTLOG_ORG_NAME", tokenStruct.OrgName)

	req := models.QueryReq{}
	err = ctx.ShouldBind(&req)
	if err != nil {
		logger.WithContext(ctx).Warnf("Get Token Struct error: %s", err)
		IOResponseUnEncode(ctx, err)
		return
	}
	logger.WithContext(ctx).Infof("Get req is %+v", req)

	byteValue, _ := json.Marshal(req)

	ctx.Set("ACTLOG_REQUEST", string(byteValue))

	if actHeaderString, err := json.Marshal(map[string]string{
		"rm-token":   tokenStruct.Content,
		"rm-session": "",
	}); err == nil {
		ctx.Set("ACTLOG_HEADER", string(actHeaderString))
	}

	level, err := s.isolateFile.GetCustomLevel(ctx, tokenStruct.OrgName, req.Sha1, tokenStruct.ClientID)
	if err == nil && level != 0 {
		//如果存在 那么就返回，如果不存在就不返回
		IOResponseUnEncode(ctx, &models.QueryResponse{
			Md5:   req.Md5,
			Level: level,
			Sha1:  req.Sha1,
		})
		return
	}

	level, extend, isFoundQAX := services.GetLevel(ctx, req.Md5, req.Sha1, "")
	if level == 0 {
		logger.WithContext(ctx).Warn("File level is 0 ")
		//IOResponseUnEncode(ctx, errors.New(102, "file level is 0 "))
		//return
	}

	resp := &models.QueryResponse{
		Md5:     req.Md5,
		Level:   level,
		Sha1:    req.Sha1,
		Extend:  extend,
		IsFound: isFoundQAX,
	}

	logger.WithContext(ctx).Infof("File getLevel resp is: %+v", resp)

	//

	//if cloudQueryOut, err := controllers.GetCloudQueryLevel(ctx, map[string]string{"md5": req.Md5, "sha1": req.Sha1}, tokenStruct.ClientID); err == nil {
	//	resp = utils.FixSecurityResponseData(resp, cloudQueryOut)
	//}

	IOResponseUnEncode(ctx, resp)

}
