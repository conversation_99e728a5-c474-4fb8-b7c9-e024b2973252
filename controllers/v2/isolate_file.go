/**
* Author: <PERSON>
* Email: wudong<PERSON>@rongma.com
* Date: 2024/1/5
* Time: 15:07
* Software: GoLand
 */

package v2

import (
	"context"
	baseErr "errors"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"rm.git/client_api/rm_common_libs.git/v2/common/protobuf/portal"
	"rm.git/client_api/rm_common_libs.git/v2/library/errors"
	"rm.git/client_api/rm_common_libs.git/v2/library/log"

	"rongma.com/srms/controllers"
	"rongma.com/srms/models"
	"rongma.com/srms/modules/client"
	"rongma.com/srms/modules/consts"
	"rongma.com/srms/modules/logger"
	"rongma.com/srms/services"
)

type IsolateFileController struct {
	service services.IIsolateFileService
}

func NewIsolateFileController(service services.IIsolateFileService) *IsolateFileController {
	return &IsolateFileController{service: service}
}

//nolint:funlen
func (s *IsolateFileController) UploadIsolateFile(ctx *gin.Context) {
	channelAcquire := portal.AcquireChannelResponse{}
	rmRequestData, err := IORequest(ctx, &channelAcquire, true, consts.SubType2)
	if err != nil {
		IOResponse(ctx, err, &channelAcquire)
		return
	}

	md5 := rmRequestData.Data["md5"]
	sha1 := rmRequestData.Data["sha1"]
	fileP := rmRequestData.Data["file_path"]
	fileName := rmRequestData.Data["file_name"]
	rootUUID := rmRequestData.Data["root_uuid"]
	orgName := rmRequestData.Token.OrgName
	clientID := rmRequestData.Token.ClientID

	if md5 == "" || sha1 == "" || fileP == "" || fileName == "" {
		logger.WithContext(ctx).Errorf("[UploadIsolateFile] InvalidParameter err: %v", err)
		IOResponse(ctx, errors.InvalidParameter, &channelAcquire)
		return
	}

	if rootUUID != "" {
		//rootUuid 处理
		rmRequestData.Data["root_uuid"] = clientID + "-" + rootUUID
	}
	// 这个参数 默认是 “1”，其中 1 代表自动，0代表 人工，兼容之前的数据为单独处理
	_, ok := rmRequestData.Data["category"]
	if !ok {
		rmRequestData.Data["category"] = "1"
	}
	_, ok = rmRequestData.Data["check_time"]
	if !ok {
		rmRequestData.Data["check_time"] = cast.ToString(time.Now().Unix())
	}

	logger.WithContext(ctx).Infof("[UploadIsolateFile] param org_name: %s , md5: %s , sha1: %s ,  file_path: %s , file_name: %s , client_id: %s , root_uuid: %s ",
		orgName, md5, sha1, fileP, fileName, clientID, rootUUID)

	req := &models.Request{}
	err = mapstructure.Decode(rmRequestData.Data, &req)
	if err != nil {
		logger.WithContext(ctx).Errorf("[UploadIsolateFile] mapstructure.Decode err: %v", err)
		IOResponse(ctx, errors.InvalidParameter, &channelAcquire)
		return
	}

	kafkaMsg := &models.KafkaMsg{
		ClientID:    rmRequestData.Token.ClientID,
		FileStatus:  consts.RecoverStatus1,
		OrgName:     rmRequestData.Token.OrgName,
		Source:      consts.IsolateSource,
		VirusType:   1,
		CheckTime:   cast.ToInt64(req.CheckTime),
		DetectionID: req.RootUUID,
		FileMd5:     strings.ToUpper(req.Md5),
		FileName:    req.FileName,
		FilePath:    req.FilePath,
		FileSha1:    strings.ToUpper(req.Sha1),
		VirusName:   req.VirusName,
		VirusSize:   cast.ToInt64(req.VirusSize),
		Category:    cast.ToInt64(req.Category),
	}

	if err := controllers.WriteToKafka(kafkaMsg); err != nil {
		logger.WithContext(ctx).Errorf("[UploadIsolateFile] mapstructure.Decode err: %v", err)
		IOResponse(ctx, errors.InvalidParameter, &channelAcquire)
		return
	}

	ctx.Set("client_id", rmRequestData.Token.ClientID)
	IOResponse(ctx, map[string]string{"message": "isolate file info upload success!"}, &channelAcquire)
	return
}

func (s *IsolateFileController) GetHostInfo(ctx *gin.Context, orgName string, clientId string) *models.HostInfo {
	filter := bson.M{"org_name": orgName, "client_id": clientId}
	info := &models.HostInfo{}

	err := client.MongoEngines.Database.Collection("hosts").FindOne(ctx, filter).Decode(&info)
	if err != nil {
		if !baseErr.Is(err, mongo.ErrNoDocuments) {
			log.Errorf("IsolateFile InfoGrpc.HostFindOne Error: %v", err)
		}
		return info
	}

	return info
}

func (s *IsolateFileController) FindInstruction(taskId string) (*models.Instructions, error) {
	res := &models.Instructions{}

	objectID, _ := primitive.ObjectIDFromHex(taskId)
	filter := bson.M{"_id": objectID}

	err := client.MongodbInstruction.Database.Collection("client_instructions").FindOne(context.Background(), filter, nil).Decode(&res)

	if err != nil {
		return res, err
	}

	return res, nil
}

func (s *IsolateFileController) UpdateIsolateFileTime(ctx *gin.Context, guid string, rootUuid string) error {

	// 更新隔离时间
	err := s.service.UpdateIsolateFileTime(ctx, guid, time.Now())
	if err != nil {
		logger.WithContext(ctx).Errorf("[UploadIsolateFile] UpdateIsolateFileTime err: %v", err)
		return err
	}
	if len(rootUuid) == 0 {
		return nil
	}
	//更新rootUuid上传时间
	isExistRootUuid, err := s.service.IsExistRootUUIDByIsolateFileExt(ctx, guid, rootUuid)
	if err != nil {
		logger.WithContext(ctx).Errorf("[UploadIsolateFile] UpdateIsolateFileTime IsExistRootUuidByIsolateFileExt err: %v", err)
		return err
	}
	if isExistRootUuid {
		//存在 则更新上报时间 不存在则添加
		err = s.service.UpdateIsolateFileExtTime(ctx, guid, rootUuid, time.Now())
		if err != nil {
			logger.WithContext(ctx).Errorf("[UploadIsolateFile] UpdateIsolateFileTime UpdateIsolateFileExtTime err: %v", err)
			return err
		}
	} else {
		err = s.insertIsolateFileExt(ctx, guid, rootUuid)
		if err != nil {
			logger.WithContext(ctx).Errorf("[UploadIsolateFile] UpdateIsolateFileTime insertIsolateFileExt err: %v", err)
			return err
		}
	}
	return nil
}

func (s *IsolateFileController) insertIsolateFileExt(ctx *gin.Context, guid string, rootUuid string) error {
	fileExt := &models.IsolateFileExt{
		IsolateFileId: guid,
		RootUuid:      rootUuid,
		CreatedTime:   time.Now().Unix(),
	}
	err := s.service.InsertIsolateFileExt(ctx, fileExt)
	if err != nil {
		logger.WithContext(ctx).Errorf("[UploadIsolateFile] InsertIsolateFileExt err: %v", err)
		return err
	}
	return nil
}
