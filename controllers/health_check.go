package controllers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"rongma.com/srms/models"
	"rongma.com/srms/modules/client"
)

type HealthCheckController struct {
}

func NewHealthCheckController() *HealthCheckController {
	return &HealthCheckController{}
}

func (c *HealthCheckController) OnHealthCheck(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, gin.H{"status": "ok"})
}

func (c *HealthCheckController) OnGetFileQueryRank(ctx *gin.Context) {
	date := ctx.Query("date")
	rank, err := strconv.Atoi(ctx.Query("rank"))
	if err != nil || date == "" {
		ctx.JSON(http.StatusOK, gin.H{"code": 1, "message": "请求参数解析失败", "data": nil})
	}

	key := fmt.Sprintf("query_file_count_%s", date)
	res := client.Redis.ZRevRangeWithScores(ctx, key, 0, int64(rank)).Val()
	if len(res) == 0 {
		ctx.JSON(http.StatusOK, gin.H{"code": 0, "message": "", "data": []interface{}{}})
		return
	}

	result := make([]models.FileQueryRankResponse, 0)
	for _, z := range res {
		rankResponse := models.FileQueryRankResponse{
			FileName: z.Member,
			Score:    z.Score,
		}
		result = append(result, rankResponse)
	}

	ctx.JSON(http.StatusOK, gin.H{"code": 0, "message": "success", "data": result})
}
