package mac

import (
	"bufio"
	"bytes"
	"compress/zlib"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"rm.git/client_api/rm_common_libs.git/v2/library/log"
	"strconv"

	"github.com/gin-gonic/gin"
	"rm.git/client_api/rm_common_libs.git/v2/common/clients/actlogger/logstruct"
	"rm.git/client_api/rm_common_libs.git/v2/common/common"
	"rm.git/client_api/rm_common_libs.git/v2/common/misc"
	"rm.git/client_api/rm_common_libs.git/v2/library/errors"

	"rongma.com/srms/modules/consts"
	"rongma.com/srms/modules/logger"

	"rongma.com/srms/config"
	"rongma.com/srms/modules/client"
)

// RMToken 定义基础接收数据格式
type RMToken struct {
	ClientID string
	Content  string
	OrgName  string
}

type RMRequestData struct {
	Data  map[string]string
	Token *common.TokenStruct
	// Session *RMSession
}

var (
	conf = config.Config()
)

/**
 * 检查请求体内容
 */
func ReadBodyContent(ctx *gin.Context) ([]byte, error) {

	var content []byte
	var err error

	if ctx.Request.ContentLength == -1 {
		return nil, errors.ContentLengthRequired
	}

	if ctx.Request.ContentLength == 0 {
		return content, nil
	}

	if content, err = io.ReadAll(bufio.NewReader(ctx.Request.Body)); err != nil {
		return nil, err
	}

	if (int64)(len(content)) != ctx.Request.ContentLength {
		return nil, errors.ContentLengthNotMatch
	}

	return content, nil

}

// 解压缩字符内容
func unZlibContent(compressSrc []byte) []byte {
	b := bytes.NewReader(compressSrc)
	var out bytes.Buffer
	r, _ := zlib.NewReader(b)
	_, _ = io.Copy(&out, r)
	return out.Bytes()
}

func actErrorLog(ctx *gin.Context, code int, res string) {
	var err logstruct.Error

	clientId := ctx.GetString("client_id")
	token := ctx.GetString("ACTLOG_TOKEN")
	orgName := ctx.GetString("ACTLOG_ORG_NAME")
	clientIp := common.GetClientIp(ctx)
	request := ctx.GetString("ACTLOG_REQUEST")
	if len(request) > conf.Service.RequestLengthThreshold {
		request = fmt.Sprintf("request body too long(> %d)", conf.Service.RequestLengthThreshold)
	}

	headerJson := ""
	if headerJsonBytes, e := json.Marshal(ctx.Request.Header); e != nil {
		logger.WithContext(ctx).Errorf("request header marshal error: %v, header info: %v", e, ctx.Request.Header)
	} else {
		headerJson = string(headerJsonBytes)
	}

	err = logstruct.Error{
		UrlPach:   ctx.Request.RequestURI,
		Method:    ctx.Request.Method,
		Header:    headerJson,
		Request:   request,
		ErrorCode: strconv.Itoa(code),
		Message:   res,
	}
	if conf.ActLogger.ActlogIndexName == "" {
		logger.WithContext(ctx).Errorf("error call actLogger config: %+v", conf.ActLogger)
	}
	client.ActLogger.Error(conf.Service.Name, ctx, err, clientId, clientIp, token, orgName, conf.ActLogger.ActlogIndexName, 1)
}

func actAccessLog(ctx *gin.Context, response string) {
	clientId := ctx.GetString("client_id")
	token := ctx.GetString("ACTLOG_TOKEN")
	orgName := ctx.GetString("ACTLOG_ORG_NAME")
	clientIp := common.GetClientIp(ctx)
	request := ctx.GetString("ACTLOG_REQUEST")
	if len(request) > conf.Service.RequestLengthThreshold {
		request = fmt.Sprintf("request body too long( > %d bytes)", conf.Service.RequestLengthThreshold)
	}
	access := logstruct.Access{
		UrlPach:  ctx.Request.RequestURI,
		Method:   ctx.Request.Method,
		Request:  request,
		Header:   ctx.GetString("ACTLOG_HEADER"),
		Response: response,
	}

	if conf.ActLogger.ActlogIndexName == "" {
		logger.WithContext(ctx).Errorf("access call actLogger config: %+v", conf.ActLogger)
	}
	client.ActLogger.Access(conf.Service.Name, ctx, access, clientId, clientIp, token, orgName, conf.ActLogger.ActlogIndexName, 1)
}

// IORequest 请求数据处理,校验rm-token
func IORequest(ctx *gin.Context) (*RMRequestData, error) {
	var (
		requestData                      = &RMRequestData{}
		clientVersion, orgName, clientId string
	)

	if clientVersion = ctx.Request.Header.Get("Client-Version"); clientVersion == "" {
		clientVersion = ctx.Query("client_version")
	}

	rmToken := ctx.GetHeader("rm-token")
	if rmToken == "" {
		log.Warn("请求头 rm-token 没有传")
		return nil, errors.InvalidToken
	}

	tokenStruct, err := common.GetTokenStruct(rmToken)
	if err != nil {
		return nil, err
	}

	orgName = tokenStruct.OrgName
	clientId = tokenStruct.ClientID
	if orgName == "" || clientId == "" {
		return nil, errors.InvalidParameter
	}

	ctx.Set("org_name", orgName)
	ctx.Set("client_id", clientId)
	ctx.Set("client_version", clientVersion)

	tokenData := common.TokenStruct{
		OrgName:  orgName,
		ClientID: clientId,
	}
	requestData.Token = &tokenData

	ctx.Set("ACTLOG_ORG_NAME", orgName)

	actHeaderString, err := json.Marshal(ctx.Request.Header)
	if err == nil {
		ctx.Set("ACTLOG_HEADER", string(actHeaderString))
	}

	return requestData, nil
}

// IOResponse 返回数据流处理
func IOResponse(ctx *gin.Context, res interface{}) {

	response := misc.NewResponse(res).SetSession(ctx.GetString("ACTLOG_SESSION")).SetRequestId(ctx.GetString(consts.HeaderXRequestID))

	jsonEncodeRes, err := json.Marshal(response)
	if err != nil {
		logger.WithContext(ctx).Errorf("Error: response json marshal error : %+v", err)
		return
	}

	if response.Error == http.StatusInternalServerError {

		actLog(ctx, response.Error, jsonEncodeRes)

		ctx.Header("Content-Type", "text/plain")
		ctx.String(http.StatusInternalServerError, "500")
	} else {

		actLog(ctx, response.Error, jsonEncodeRes)
		ctx.Header("Content-Type", "text/plain")
		ctx.String(http.StatusOK, string(jsonEncodeRes))
	}

}

// logger.WithContext(ctx) 打印
func actLog(ctx *gin.Context, code int, jsonEncodeRes []byte) {
	if code == 0 {
		actAccessLog(ctx, string(jsonEncodeRes))
	} else {
		actErrorLog(ctx, code, string(jsonEncodeRes))
	}
}
