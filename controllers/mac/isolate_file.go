/**
* Author: <PERSON>
* Email: <EMAIL>
* Date: 2024/1/5
* Time: 15:07
* Software: GoLand
 */

package mac

import (
	"context"
	baseErr "errors"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-basic/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"rm.git/client_api/rm_common_libs.git/v2/library/errors"
	"rm.git/client_api/rm_common_libs.git/v2/library/log"

	"rongma.com/srms/models"
	"rongma.com/srms/modules/client"
	"rongma.com/srms/modules/consts"
	"rongma.com/srms/modules/logger"
	"rongma.com/srms/services"
)

type IsolateFileControllerMac struct {
	service services.IIsolateFileService
}

func NewIsolateFileControllerMac(service services.IIsolateFileService) *IsolateFileControllerMac {
	return &IsolateFileControllerMac{service: service}
}

//nolint:funlen
func (s *IsolateFileControllerMac) UploadIsolateFile(ctx *gin.Context) {
	// channelAcquire := portal.AcquireChannelResponse{}
	rmRequestData, err := IORequest(ctx)
	if err != nil {
		IOResponse(ctx, err)
		return
	}

	req := &models.IsolateFileReq{}
	err = ctx.ShouldBind(&req)
	if err != nil {
		IOResponse(ctx, errors.InvalidParameter)
		return
	}

	orgName := rmRequestData.Token.OrgName
	clientID := rmRequestData.Token.ClientID

	if req.RootUUID != "" {
		//rootUuid 处理
		req.RootUUID = clientID + "-" + req.RootUUID
	}

	logger.WithContext(ctx).Infof("[UploadIsolateFile] param org_name: %s , md5: %s , sha1: %s ,  file_path: %s , file_name: %s , client_id: %s , root_uuid: %s ",
		orgName, req.Md5, req.ShA1, req.FilePath, req.FileName, clientID, req.RootUUID)

	//校验文件是否存在(存在取最后一条) org_name+md5+sha1+client_id 唯一
	isolateFile, err := s.service.FindIsolateFileByOne(ctx, orgName, strings.ToUpper(req.Md5), strings.ToUpper(req.ShA1), clientID, req.FilePath)
	if err != nil {
		logger.WithContext(ctx).Errorf("[UploadIsolateFile] FindIsolateFileByOne  query err: %v", err)
		IOResponse(ctx, err)
		return
	}
	insertFlag := false
	timeNow := time.Now()
	if isolateFile != nil && isolateFile.GUID != "" {
		//文件存在
		//recover_status=1 则不添加直接返回 需更新最近一次操作时间
		//recover_status = 2 或者 recover_status = 3 则取响应状态 如果是success状态 则新增一条，其他状态则不新增
		if isolateFile.RecoverStatus == consts.RecoverStatus1 {
			//更新最后一次隔离时间
			_ = s.UpdateIsolateFileTime(ctx, isolateFile.GUID, req.RootUUID)

			logger.WithContext(ctx).Info("[UploadIsolateFile] isolate file exist")
			IOResponse(ctx, err)
			return
		} else if isolateFile.RecoverStatus == consts.RecoverStatus2 ||
			isolateFile.RecoverStatus == consts.RecoverStatus3 {

			if isolateFile.TaskId != "" && req.RootUUID != "" {
				instruction, err := s.FindInstruction(isolateFile.TaskId)
				if err != nil {
					logger.WithContext(ctx).Infof("[UploadIsolateFile] instruction.FindInstruction err: %v", err)
					IOResponse(ctx, err)
					return
				}
				if instruction.Status != consts.InstructionStatus2 {
					//更新最后一次隔离时间
					_ = s.UpdateIsolateFileTime(ctx, isolateFile.GUID, req.RootUUID)

					logger.WithContext(ctx).Info("[UploadIsolateFile] isolate file exist")
					IOResponse(ctx, err)
					return
				}
			}
			insertFlag = true
		}
	} else {
		insertFlag = true
	}

	isolateFileId := ""
	if insertFlag {
		hostInfo := s.GetHostInfo(ctx, orgName, clientID)
		isolateFileId = uuid.New()
		timeN := timeNow.Unix()
		file := &models.IsolateFile{
			GUID:               isolateFileId,
			OrgName:            orgName,
			ClientId:           clientID,
			Md5:                strings.ToUpper(req.Md5),
			Sha1:               strings.ToUpper(req.ShA1),
			FileName:           req.FileName,
			HostName:           hostInfo.HostName,
			Username:           hostInfo.Username,
			RecoverStatus:      consts.RecoverStatus1,
			LastQuarantineTime: timeN,
			CreateTime:         timeN,
			Category:           req.Category,
			FilePath:           req.FilePath,
		}
		err = s.service.InsertIsolateFile(ctx, file)
		if err != nil {
			logger.WithContext(ctx).Errorf("[UploadIsolateFile] InsertIsolateFile insert err: %v", err)
			IOResponse(ctx, err)
			return
		}
	}
	if req.RootUUID != "" {
		//添加隔离文件
		isExistRootUuid, err := s.service.IsExistRootUUIDByIsolateFileExt(ctx, isolateFileId, req.RootUUID)
		if isExistRootUuid {
			logger.WithContext(ctx).Infof("[UploadIsolateFile] IsExistRootUuidByIsolateFileExt exist: %v", err)
			IOResponse(ctx, err)
			return
		}
		//添加隔离文件rootUuid信息
		err = s.insertIsolateFileExt(ctx, isolateFileId, req.RootUUID)
		if err != nil {
			logger.WithContext(ctx).Errorf("[UploadIsolateFile] InsertIsolateFileExt insert err: %v", err)
			IOResponse(ctx, err)
			return
		}
	}

	ctx.Set("client_id", rmRequestData.Token.ClientID)
	IOResponse(ctx, map[string]string{"message": "isolate file info upload success!"})
	return
}

func (s *IsolateFileControllerMac) GetHostInfo(ctx *gin.Context, orgName string, clientId string) *models.HostInfo {
	filter := bson.M{"org_name": orgName, "client_id": clientId}
	info := &models.HostInfo{}
	err := client.MongoEngines.Database.Collection("hosts").FindOne(ctx, filter).Decode(&info)
	if err != nil {
		if !baseErr.Is(err, mongo.ErrNoDocuments) {
			log.Errorf("IsolateFile InfoGrpc.HostFindOne Error: %v", err)
		}
		return info
	}

	return info
}

func (s *IsolateFileControllerMac) FindInstruction(taskId string) (*models.Instructions, error) {
	res := &models.Instructions{}

	objectID, _ := primitive.ObjectIDFromHex(taskId)
	filter := bson.M{"_id": objectID}
	err := client.MongodbInstruction.Database.Collection("client_instructions").FindOne(context.Background(), filter, nil).Decode(&res)
	if err != nil {
		return res, err
	}

	return res, nil
}

func (s *IsolateFileControllerMac) UpdateIsolateFileTime(ctx *gin.Context, guid string, rootUuid string) error {
	// 更新隔离时间
	err := s.service.UpdateIsolateFileTime(ctx, guid, time.Now())
	if err != nil {
		logger.WithContext(ctx).Errorf("[UploadIsolateFile] UpdateIsolateFileTime err: %v", err)
		return err
	}

	if rootUuid == "" {
		return nil
	}

	//更新rootUuid上传时间
	isExistRootUuid, err := s.service.IsExistRootUUIDByIsolateFileExt(ctx, guid, rootUuid)
	if err != nil {
		logger.WithContext(ctx).Errorf("[UploadIsolateFile] UpdateIsolateFileTime IsExistRootUuidByIsolateFileExt err: %v", err)
		return err
	}
	if isExistRootUuid {
		//存在 则更新上报时间 不存在则添加
		err = s.service.UpdateIsolateFileExtTime(ctx, guid, rootUuid, time.Now())
		if err != nil {
			logger.WithContext(ctx).Errorf("[UploadIsolateFile] UpdateIsolateFileTime UpdateIsolateFileExtTime err: %v", err)
			return err
		}
	} else {
		err = s.insertIsolateFileExt(ctx, guid, rootUuid)
		if err != nil {
			logger.WithContext(ctx).Errorf("[UploadIsolateFile] UpdateIsolateFileTime insertIsolateFileExt err: %v", err)
			return err
		}
	}
	return nil
}

func (s *IsolateFileControllerMac) insertIsolateFileExt(ctx *gin.Context, guid string, rootUuid string) error {
	fileExt := &models.IsolateFileExt{
		IsolateFileId: guid,
		RootUuid:      rootUuid,
		CreatedTime:   time.Now().Unix(),
	}
	err := s.service.InsertIsolateFileExt(ctx, fileExt)
	if err != nil {
		logger.WithContext(ctx).Errorf("[UploadIsolateFile] InsertIsolateFileExt err: %v", err)
		return err
	}
	return nil
}
