package mac

import (
	"bytes"
	"compress/zlib"
	"io"

	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/util/gconv"
	"rm.git/client_api/rm_common_libs.git/v2/common/common"
	"rm.git/client_api/rm_common_libs.git/v2/library/errors"
	"rm.git/client_api/rm_common_libs.git/v2/library/log"

	"rongma.com/srms/controllers"
	"rongma.com/srms/models"
	"rongma.com/srms/modules/consts"
	"rongma.com/srms/services"
	"rongma.com/srms/utils"
)

type SRMSControllerMac struct {
	service     services.ISRMSService
	isolateFile services.IIsolateFileService
}

func NewSRMSControllerMac(service services.ISRMSService, isolate services.IIsolateFileService) *SRMSControllerMac {
	return &SRMSControllerMac{service: service, isolateFile: isolate}
}

// OnQueryFileSecurity 样本文件查询
func (s *SRMSControllerMac) OnQueryFileSecurity(ctx *gin.Context) {

	rmRequestData, err := IORequest(ctx)
	if err != nil {
		IOResponse(ctx, err)
		return
	}

	req := &models.FileSecurityQueryRequest{}
	err = ctx.ShouldBind(&req)
	if err != nil {
		IOResponse(ctx, errors.InvalidParameter)
		return
	}

	level, err := s.isolateFile.GetCustomLevel(ctx, rmRequestData.Token.OrgName, req.SHA1, rmRequestData.Token.ClientID)
	if err == nil && level != 0 {
		//如果存在 那么就返回，如果不存在就不返回
		resp := &models.FileSecurityQueryResponse{MD5: req.MD5, Level: level, NeedUpload: 0, SHA1: req.SHA1, LevelSource: consts.LevelSourceIocExclusion}
		IOResponse(ctx, resp)
		return
	}

	rmRequestData.Data = gconv.MapStrStr(req)

	ctx.Set("ACTLOG_REQUEST", rmRequestData.Data)

	resp, err := s.service.OnQueryFileSecurity(ctx, rmRequestData.Data, &models.RequestAuth{
		ClientID: rmRequestData.Token.ClientID,
		ClientIP: common.GetClientIp(ctx),
		Channel:  ctx.Query("channel"),
		Token:    ctx.GetString("ACTLOG_TOKEN"),
		OrgName:  rmRequestData.Token.OrgName,
	})

	if err != nil {
		IOResponse(ctx, err)
		return
	}
	// 开关关闭，无论如何都不能上传
	if conf.Service.UploadSwitch != "on" {
		resp.NeedUpload = 0
	}
	resp.LevelSource = consts.LevelSourceRmMd5

	if cloudQueryOut, err := controllers.GetCloudQueryLevel(ctx, rmRequestData.Data, rmRequestData.Token.ClientID); err == nil {
		resp = utils.FixSecurityResponseData(resp, cloudQueryOut)
	}

	IOResponse(ctx, resp)
}

// OnUpload 样本文件上传请求
func (s *SRMSControllerMac) OnUpload(ctx *gin.Context) {

	rmRequestData, err := IORequest(ctx)
	if err != nil {
		IOResponse(ctx, err)
		return
	}

	req := &models.FileUploadRequest{}
	err = ctx.ShouldBind(&req)
	if err != nil {
		IOResponse(ctx, errors.InvalidParameter)
		return
	}
	req.ClientId = rmRequestData.Token.ClientID
	req.OrgName = rmRequestData.Token.OrgName

	rmRequestData.Data = gconv.MapStrStr(req)
	ctx.Set("ACTLOG_REQUEST", rmRequestData.Data)

	resp, err := s.service.OnUpload(ctx, rmRequestData.Data, &models.RequestAuth{
		ClientID: rmRequestData.Token.ClientID,
		ClientIP: common.GetClientIp(ctx),
		Channel:  ctx.Query("channel"),
		Token:    ctx.GetString("ACTLOG_TOKEN"),
		OrgName:  rmRequestData.Token.OrgName,
	})

	if err != nil {
		IOResponse(ctx, err)
		return
	}
	IOResponse(ctx, resp)

}

// OnUploadShard 样本文件上传
func (s *SRMSControllerMac) OnUploadShard(ctx *gin.Context) {

	rmRequestData, err := IORequest(ctx)
	if err != nil {
		IOResponse(ctx, err)
		return
	}
	// 读取请求体
	bodyBytes, err := ReadBodyContent(ctx)
	if err != nil {
		IOResponse(ctx, err)
		return
	}
	var bodyContent []byte
	// 解压缩处理
	headerStr := ctx.GetHeader("Content-Type")
	if headerStr == "application/x-zip-compressed" {
		bodyContent, err = ZlibUnCompress(string(bodyBytes))
		if err != nil {
			log.Errorf("zlib unCompress error : %v", err)
			IOResponse(ctx, errors.InvalidParameter)
			return
		}
	} else {
		bodyContent = bodyBytes
	}

	fileToken := ctx.Query("file_token")
	if fileToken == "" {
		IOResponse(ctx, errors.InvalidParameter)
		return
	}

	resp, err := s.service.OnUploadShard(ctx, string(bodyContent), fileToken, &models.RequestAuth{
		ClientID: rmRequestData.Token.ClientID,
		ClientIP: common.GetClientIp(ctx),
		Channel:  ctx.Query("channel"),
		Token:    ctx.GetString("ACTLOG_TOKEN"),
		OrgName:  rmRequestData.Token.OrgName,
	})

	if err != nil {
		IOResponse(ctx, err)
		return
	}

	IOResponse(ctx, resp)

}

func ZlibUnCompress(compressSrc string) ([]byte, error) {
	b := bytes.NewReader([]byte(compressSrc))
	var out bytes.Buffer
	r, err := zlib.NewReader(b)
	if err != nil {
		return nil, err
	}
	io.Copy(&out, r)
	return out.Bytes(), nil
}
