package controllers

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"rm.git/client_api/rm_common_libs.git/v2/common/protobuf/portal"
	"rm.git/client_api/rm_common_libs.git/v2/library/errors"

	"rongma.com/srms/config"
	"rongma.com/srms/models"
	"rongma.com/srms/modules/logger"
	"rongma.com/srms/services"
	"rongma.com/srms/utils"
)

var (
	conf = config.Config()
)

type SRMSController struct {
	service services.ISRMSService
}

func NewSRMSController(service services.ISRMSService) *SRMSController {
	return &SRMSController{service: service}
}

// OnQueryFileSecurity 样本文件查询
func (s *SRMSController) OnQueryFileSecurity(ctx *gin.Context) {
	clientId := ctx.Query("client_id")
	channelId := ctx.Query("channel")
	//orgName := ctx.Query("org_name")
	resMessage := portal.AcquireChannelResponse{}
	logger.WithContext(ctx).Infof("[query] client_id: %s, channelId: %s ", clientId, channelId)

	ctx.Set("client_id", clientId)

	bodyContent, err := CheckBodyContent(ctx) // 检验内容是否符合,返回元数据二进制
	if err != nil {
		logger.WithContext(ctx).Errorf("[query] check body content error, %s", err)
		Response(ctx, err)
		return
	}

	var parseContentStr string

	if parseContentStr, err = s.service.DecodeRequestBody(ctx, channelId, &resMessage, bodyContent); err != nil { // 解密内容
		logger.WithContext(ctx).Errorf("client_id:%s, v1DecodeRequestBody-err: %s", clientId, err)
		Response(ctx, err)
		return
	}

	ctx.Set("ACTLOG_REQUEST", parseContentStr)

	// json解析出请求内容
	jsonRequestString, err := FormatBodyContent(ctx, []byte(parseContentStr))
	if err != nil {
		logger.WithContext(ctx).Errorf("FormatBodyContent err: %s", err)
		Response(ctx, errors.ContentFormatNotMatch)
		return
	}

	// json解析出请求内容
	modelRequest := &models.FileSecurityQueryRequest{}
	if err = json.Unmarshal(jsonRequestString, modelRequest); err != nil {
		logger.WithContext(ctx).Errorf("[query] json unmarshal error, %s", err)
		Response(ctx, errors.ContentFormatNotMatch)
		return
	}

	modelRequest = utils.FileSecurityFieldCompatible(modelRequest)
	if modelRequest.MD5 == "" {
		logger.WithContext(ctx).Errorf("[query] md5 is null")
		Response(ctx, errors.InvalidParameter)
		return
	}

	maliceMap := make(map[string]string)
	newMap := make(map[string]string)
	_ = json.Unmarshal(jsonRequestString, &maliceMap)
	for k, v := range maliceMap {
		lowerKey := strings.ToLower(k)
		lowerValue := strings.ToLower(v)
		newMap[lowerKey] = lowerValue
	}

	fileName := utils.GetFileName(newMap)

	var (
		level      = 0
		needUpload = 0
		extend     = make(map[string]interface{})
	)

	level, extend, _ = s.getLevel(ctx, modelRequest.MD5, modelRequest.SHA1, fileName)
	if conf.Service.UploadSwitch == "on" {
		needUpload = 0
	}

	commandLineFileLevel := 0
	commandLineFileNeedUpload := 0

	if modelRequest.CommandLineFile != "" &&
		(modelRequest.CommandLineFileMd5 != "" || modelRequest.CommandLineFileSha1 != "") {
		commandLineFileLevel, extend, _ = s.getLevel(ctx, modelRequest.CommandLineFileMd5, modelRequest.CommandLineFileSha1, fileName)
		if conf.Service.UploadSwitch == "on" {
			commandLineFileNeedUpload = 0
		}
	}

	// 返回结果
	res := &models.FileSecurityQueryResponse{
		MD5:                       modelRequest.MD5,
		SHA1:                      modelRequest.SHA1,
		SHA256:                    modelRequest.SHA256,
		Level:                     level,
		NeedUpload:                needUpload,
		CommandLineFileMD5:        modelRequest.CommandLineFileMd5,
		CommandLineFileSHA1:       modelRequest.CommandLineFileSha1,
		CommandLineFileLevel:      commandLineFileLevel,
		CommandLineFileNeedUpload: commandLineFileNeedUpload,
		Extend:                    extend,
	}

	if utils.IntInArray(conf.Service.CheckLevel, level) {

		if newMap["s_operation"] == "loadimage" {
			newMap["i_newimagelevel"] = strconv.Itoa(level)
		} else if newMap["s_operation"] == "createprocess" {
			newMap["i_newprocesslevel"] = strconv.Itoa(level)
			newMap["i_commandlinefilelevel"] = strconv.Itoa(commandLineFileLevel)
		}

		if cloudQueryOut, e := s.getCloudQueryLevel(ctx, newMap, clientId); e == nil {
			res = utils.FixSecurityResponseData(res, cloudQueryOut)
		}
	}

	jsonEncodeRes, err := json.Marshal(res)
	if err != nil {
		logger.WithContext(ctx).Errorf("[query] json marshal error: %s", err)
		Response(ctx, err)
		return
	}
	ResponseRes, err := EncryptResponse(jsonEncodeRes, resMessage.SecretKey)
	if err != nil {
		logger.WithContext(ctx).Errorf("[query] encrypt response error, %s", err)
		Response(ctx, err)
		return
	}

	ctx.Set("ACTLOG_RESPONSE", string(jsonEncodeRes))

	Response(ctx, ResponseRes)
}

// 根据md5（必填） 和sha1（可选） 获取文件的等级
func (s *SRMSController) getLevel(ctx context.Context, md5 string, sha1 string, fileName string) (int, map[string]interface{}, bool) {
	return services.GetLevel(ctx, md5, sha1, fileName)
}

func (s *SRMSController) getCloudQueryLevel(ctx context.Context, req map[string]string, clientId string) (map[string]interface{}, error) {
	return GetCloudQueryLevel(ctx, req, clientId)
}

// OnUpload 样本文件上传请求
func (s *SRMSController) OnUpload(ctx *gin.Context) {
	clientId := ctx.Query("client_id")
	channelId := ctx.Query("channel")

	resMessage := portal.AcquireChannelResponse{}
	logger.WithContext(ctx).Infof("[upload] client_id: %s, channelId: %s ", clientId, channelId)

	ctx.Set("client_id", clientId)

	bodyContent, err := CheckBodyContent(ctx) // 检验内容是否符合,返回元数据二进制
	if err != nil {
		logger.WithContext(ctx).Errorf("[upload] check body content error, %s", err)
		Response(ctx, err)
		return
	}

	var parseContentStr string

	if parseContentStr, err = s.service.DecodeRequestBody(ctx, channelId, &resMessage, bodyContent); err != nil { // 解密内容
		Response(ctx, err)
		return
	}

	ctx.Set("ACTLOG_REQUEST", parseContentStr)
	//v1接口直接返回结果
	res := models.FileUploadResponse{}

	jsonEncodeRes, err := json.Marshal(res)
	if err != nil {
		Response(ctx, err)
		return
	}

	ResponseRes, err := EncryptResponse(jsonEncodeRes, resMessage.SecretKey)
	if err != nil {
		logger.WithContext(ctx).Errorf("[upload] encrypt response error, %s", err)
		Response(ctx, err)
		return
	}

	ctx.Set("ACTLOG_RESPONSE", string(jsonEncodeRes))

	Response(ctx, ResponseRes)
}

// OnUploadShard 样本文件上传
func (s *SRMSController) OnUploadShard(ctx *gin.Context) {
	channelId := ctx.Query("channel")
	clientId := ctx.Query("client_id")
	fileToken := ctx.Query("file_token")
	partnerId := ctx.Query("partner_id") // todo 合作者id，用于后期可以根据客户来配置上传频率

	logger.WithContext(ctx).Infof("[shard] partner_id: %s, channel: %s, client_id: %s, file_token: %s", partnerId, channelId, clientId, fileToken)

	ctx.Set("client_id", clientId)
	ctx.Set("ACTLOG_REQUEST", "")

	//// 如果当前channelId超过上传限制，则直接返回
	resMessage := portal.AcquireChannelResponse{}

	bodyContent, err := CheckBodyContent(ctx) // 检验内容是否符合,返回元数据二进制
	if err != nil {
		logger.WithContext(ctx).Errorf("[shard] check content err: %v ", err)
		Response(ctx, err)
		return
	}

	modelRequest := &models.FileShardUploadRequest{}
	modelRequest.Token = ctx.Query("file_token")

	var parseContentStr string

	if parseContentStr, err = s.service.DecodeRequestBody(ctx, channelId, &resMessage, bodyContent); err != nil { // 解密内容
		logger.WithContext(ctx).Errorf("[shard] file decode error: parse length: %v , req len:%v", len(parseContentStr), ctx.Request.ContentLength)
		Response(ctx, err)
		return
	}

	// 解密后数据进行解压缩处理
	headerStr := ctx.GetHeader("Content-Type")
	modelRequest.Data = parseContentStr

	if headerStr == "application/x-zip-compressed" {
		bodyContent, err := ZlibUnCompress(parseContentStr)
		if err != nil {
			logger.WithContext(ctx).Errorf("[shard] zlib uncompress error : %v", err)
			Response(ctx, err)
			return
		}
		modelRequest.Data = string(bodyContent)
	}

	if len(modelRequest.Data) == 0 || len(modelRequest.Token) == 0 {
		logger.WithContext(ctx).Errorf("[shard] parameter error, token:%s, data length:%d", modelRequest.Token, len(modelRequest.Data))
		Response(ctx, errors.InvalidParameter)
		return
	}

	res := &models.FileShardUploadResponse{}
	// if conf.Service.Encrypt == true { //开启加密
	jsonEncodeRes, err := json.Marshal(res)
	if err != nil {
		logger.WithContext(ctx).Errorf("[shard] Json Marshal err: %v ", err)
		Response(ctx, err)
		return
	}

	ResponseRes, err := EncryptResponse(jsonEncodeRes, resMessage.SecretKey)
	if err != nil {
		logger.WithContext(ctx).Errorf("[shard] EncryptResponse err: %v ", err)
		Response(ctx, err)
		return
	}

	ctx.Set("ACTLOG_RESPONSE", string(jsonEncodeRes))

	Response(ctx, ResponseRes)
}

// OnDumpUpload 上传dump文件
func (s *SRMSController) OnDumpUpload(ctx *gin.Context) {
	channelId := ctx.Query("channel")
	clientId := ctx.Query("client_id")
	partnerId := ctx.Query("partner_id") // todo 合作者id，用于后期可以根据客户来配置上传频率
	fileName := ctx.Query("file_name")
	OrgName := ctx.Query("org_name")

	ctx.Set("ACTLOG_REQUEST", "")
	ctx.Set("client_id", clientId)

	if fileName == "" {
		logger.WithContext(ctx).Errorf("[dump] lost file_name parameter")
		Response(ctx, errors.InvalidParameter)
		return
	}

	resMessage := portal.AcquireChannelResponse{}
	bodyContent, err := CheckBodyContent(ctx) // 检验内容是否符合,返回元数据二进制
	if err != nil {
		logger.WithContext(ctx).Errorf("[dump] check content err: %v ", err)
		Response(ctx, err)
		return
	}

	modelRequest := &models.DumpUploadRequest{}
	modelRequest.FileName = fmt.Sprintf("dump_%s_%s_%s.file", partnerId, clientId, fileName) // ctx.Query("file_name")
	var parseContentStr string
	if parseContentStr, err = s.service.DecodeRequestBody(ctx, channelId, &resMessage, bodyContent); err != nil { // 解密内容
		logger.WithContext(ctx).Errorf("[dump] file decode error: parse length: %v , req len:%v", len(parseContentStr), ctx.Request.ContentLength)
		Response(ctx, err)
		return
	}
	// 解密后数据进行解压缩处理
	headerStr := ctx.GetHeader("Content-Type")
	modelRequest.Data = parseContentStr

	if headerStr == "application/x-zip-compressed" {
		bodyContent, err := ZlibUnCompress(parseContentStr)
		if err != nil {
			logger.WithContext(ctx).Errorf("[dump] zlib unCompress error : %v", err)
			Response(ctx, err)
			return
		}
		modelRequest.Data = string(bodyContent)
	}

	if modelRequest.Data == "" || modelRequest.FileName == "" {
		logger.WithContext(ctx).Errorf("[dump] parameter error, file_name:%s, data length:%d", modelRequest.FileName, len(modelRequest.Data))
		Response(ctx, errors.InvalidParameter)
		return
	}

	modelRequest.OrgName = OrgName
	res := models.DumpUploadResponse{}
	//res, err := s.service.OnDumpUpload(ctx, modelRequest) // 调用上传方法，实现功能逻辑
	//if err != nil {
	//	logger.WithContext(ctx).Errorf("[dump] upload shard error:%s", err)
	//	Response(ctx, err)
	//	return
	//}

	jsonEncodeRes, err := json.Marshal(res)
	if err != nil {
		logger.WithContext(ctx).Errorf("[dump] Json Marshal err: %v ", err)
		Response(ctx, err)
		return
	}

	ResponseRes, err := EncryptResponse(jsonEncodeRes, resMessage.SecretKey)
	if err != nil {
		logger.WithContext(ctx).Errorf("[dump] EncryptResponse err: %v ", err)
		Response(ctx, err)
		return
	}

	ctx.Set("ACTLOG_RESPONSE", string(jsonEncodeRes))

	Response(ctx, ResponseRes)
}

func (s *SRMSController) OnGetDumpConfig(ctx *gin.Context) {

	clientId := ctx.Query("client_id")
	channelId := ctx.Query("channel")
	// partnerId := ctx.Query("partner_id")
	resMessage := portal.AcquireChannelResponse{}

	ctx.Set("client_id", clientId)

	bodyContent, err := CheckBodyContent(ctx) // 检验内容是否符合,返回元数据二进制
	if err != nil {
		logger.WithContext(ctx).Errorf("[query] check body content error, %s", err)
		Response(ctx, err)
		return
	}

	var parseContentStr string

	// if conf.Service.Encrypt == true { //开启加密
	if parseContentStr, err = s.service.DecodeRequestBody(ctx, channelId, &resMessage, bodyContent); err != nil { // 解密内容
		Response(ctx, err)
		return
	}
	// } else {
	//	parseContentStr = string(bodyContent)
	// }

	// logger.WithContext(ctx).Infof("[parseContent %s ]", parseContentStr)
	// json解析出请求内容
	jsonRequestString, err := FormatBodyContent(ctx, []byte(parseContentStr))
	if err != nil {
		Response(ctx, errors.ContentFormatNotMatch)
		return
	}
	ctx.Set("ACTLOG_REQUEST", parseContentStr)
	// json解析出请求内容
	modelRequest := &models.DumpConfigRequest{}
	if err = json.Unmarshal(jsonRequestString, modelRequest); err != nil {
		logger.WithContext(ctx).Errorf("[query] json unmarshal error, %s", err)
		Response(ctx, errors.ContentFormatNotMatch)
		return
	}

	if modelRequest.ProductVer == "" {
		logger.WithContext(ctx).Errorf("[query] product version is null")
		Response(ctx, errors.InvalidParameter)
		return
	}
	res := &models.DumpConfigResponse{}
	//res, err = s.service.OnGetDumpConfig(ctx, modelRequest) // 调用上传方法，实现功能逻辑
	//if err != nil {
	//	logger.WithContext(ctx).Errorf("[query] query dump config error, %s", err)
	//	Response(ctx, err)
	//	return
	//}

	// if conf.Service.Encrypt == true { //开启加密
	jsonEncodeRes, err := json.Marshal(res)
	if err != nil {
		logger.WithContext(ctx).Errorf("[query] json marshal error: %s", err)
		Response(ctx, err)
		return
	}

	ResponseRes, err := EncryptResponse(jsonEncodeRes, resMessage.SecretKey)
	if err != nil {
		logger.WithContext(ctx).Errorf("[query] encrypt response error, %s", err)
		Response(ctx, err)
		return
	}

	ctx.Set("ACTLOG_RESPONSE", string(jsonEncodeRes))

	Response(ctx, ResponseRes)
}

func (s *SRMSController) OnGetUploadConfig(ctx *gin.Context) {
	channelId := ctx.Query("channel")
	clientId := ctx.Query("client_id")

	resMessage := portal.AcquireChannelResponse{}

	ctx.Set("client_id", clientId)

	bodyContent, err := CheckBodyContent(ctx) // 检验内容是否符合,返回元数据二进制
	if err != nil {
		logger.WithContext(ctx).Errorf("[query] check body content error, %s", err)
		Response(ctx, err)
		return
	}

	var parseContentStr string

	if parseContentStr, err = s.service.DecodeRequestBody(ctx, channelId, &resMessage, bodyContent); err != nil { // 解密内容
		Response(ctx, err)
		return
	}
	// json解析出请求内容
	jsonRequestString, err := FormatBodyContent(ctx, []byte(parseContentStr))
	if err != nil {
		Response(ctx, errors.ContentFormatNotMatch)
		return
	}
	ctx.Set("ACTLOG_REQUEST", parseContentStr)
	// json解析出请求内容
	modelRequest := &models.UploadConfigRequest{}
	if err = json.Unmarshal(jsonRequestString, modelRequest); err != nil {
		logger.WithContext(ctx).Errorf("[query] json unmarshal error, %s", err)
		Response(ctx, errors.ContentFormatNotMatch)
		return
	}

	cf := conf.Uploads
	res := models.UploadConfigResponse{
		Quantity:        cf.Quantity,
		IntervalTime:    cf.IntervalTime,
		APIIntervalTime: cf.APIIntervalTime,
	}

	jsonEncodeRes, err := json.Marshal(res)
	if err != nil {
		logger.WithContext(ctx).Errorf("[query] json marshal error: %s", err)
		Response(ctx, err)
		return
	}

	ResponseRes, err := EncryptResponse(jsonEncodeRes, resMessage.SecretKey)
	if err != nil {
		logger.WithContext(ctx).Errorf("[query] encrypt response error, %s", err)
		Response(ctx, err)
		return
	}

	ctx.Set("ACTLOG_RESPONSE", string(jsonEncodeRes))

	Response(ctx, ResponseRes)
}

func (s *SRMSController) OnQuarantineFileInform(ctx *gin.Context) {
	channelId := ctx.Query("channel")
	clientId := ctx.Query("client_id")
	//orgName := ctx.Query("org_name")
	// partnerId := ctx.Query("partner_id") //todo 合作者id，用于后期可以根据客户来配置上传频率

	ctx.Set("ACTLOG_REQUEST", "")
	ctx.Set("client_id", clientId)

	resMessage := portal.AcquireChannelResponse{}
	bodyContent, err := CheckBodyContent(ctx) // 检验内容是否符合,返回元数据二进制
	if err != nil {
		logger.WithContext(ctx).Errorf("quarantine file upload check content err: %v ", err)
		Response(ctx, err)
		return
	}

	var parseContentStr string
	if parseContentStr, err = s.service.DecodeRequestBody(ctx, channelId, &resMessage, bodyContent); err != nil { // 解密内容
		logger.WithContext(ctx).Errorf("quarantine file decode error: parse length: %v , req len:%v", len(parseContentStr), ctx.Request.ContentLength)
		Response(ctx, err)
		return
	}

	modelRequest := make([]models.QuarantineFileRequest, 0)
	if err = json.Unmarshal([]byte(parseContentStr), &modelRequest); err != nil {
		logger.WithContext(ctx).Error("json.unmarshal quarantine file data fail:", err)
	}

	if len(modelRequest) > conf.Uploads.Quantity {
		Response(ctx, errors.FileCountOutOfConfig)
		return
	}

	res := map[string]interface{}{
		"msg": "quarantine file info upload success!",
	}
	//res, err := s.service.OnQuarantineFileInform(ctx, modelRequest, clientId, orgName)
	//if err != nil {
	//	Response(ctx, err)
	//	return
	//}

	jsonEncodeRes, err := json.Marshal(res)
	if err != nil {
		Response(ctx, err)
		return
	}

	ResponseRes, err := EncryptResponse(jsonEncodeRes, resMessage.SecretKey)
	if err != nil {
		Response(ctx, err)
		return
	}

	ctx.Set("ACTLOG_RESPONSE", string(jsonEncodeRes))

	Response(ctx, ResponseRes)
}

func (s *SRMSController) OnTrustFileInform(ctx *gin.Context) {
	channelId := ctx.Query("channel")
	clientId := ctx.Query("client_id")
	//orgName := ctx.Query("org_name")
	// partnerId := ctx.Query("partner_id") //todo 合作者id，用于后期可以根据客户来配置上传频率

	ctx.Set("ACTLOG_REQUEST", "")
	ctx.Set("client_id", clientId)

	resMessage := portal.AcquireChannelResponse{}
	bodyContent, err := CheckBodyContent(ctx) // 检验内容是否符合,返回元数据二进制
	if err != nil {
		logger.WithContext(ctx).Errorf("trust file upload check content err: %v ", err)
		Response(ctx, err)
		return
	}

	var parseContentStr string
	if parseContentStr, err = s.service.DecodeRequestBody(ctx, channelId, &resMessage, bodyContent); err != nil { // 解密内容
		logger.WithContext(ctx).Errorf("trust file decode error: parse length: %v , req len:%v", len(parseContentStr), ctx.Request.ContentLength)
		Response(ctx, err)
		return
	}

	modelRequest := make([]models.TrustFileRequest, 0)
	if err = json.Unmarshal([]byte(parseContentStr), &modelRequest); err != nil {
		logger.WithContext(ctx).Error("json.unmarshal trust file data fail:", err)
		Response(ctx, err)
		return
	}

	if len(modelRequest) > conf.Uploads.Quantity {
		Response(ctx, errors.FileCountOutOfConfig)
		return
	}

	res := map[string]interface{}{
		"msg": "trust file info upload success!",
	}
	//res, err := s.service.OnTrustFileInform(ctx, modelRequest, clientId, orgName)
	//if err != nil {
	//	Response(ctx, err)
	//	return
	//}

	jsonEncodeRes, err := json.Marshal(res)
	if err != nil {
		Response(ctx, err)
		return
	}

	ResponseRes, err := EncryptResponse(jsonEncodeRes, resMessage.SecretKey)
	if err != nil {
		Response(ctx, err)
		return
	}

	ctx.Set("ACTLOG_RESPONSE", string(jsonEncodeRes))

	Response(ctx, ResponseRes)
}
