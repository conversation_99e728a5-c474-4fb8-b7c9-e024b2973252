package controllers

import (
	"bytes"
	"compress/zlib"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"rm.git/client_api/rm_common_libs.git/v2/library/log"
	"strings"

	"github.com/gin-gonic/gin"
	"rm.git/client_api/rm_common_libs.git/v2/common/misc"
	"rm.git/client_api/rm_common_libs.git/v2/library/cipher"
	"rm.git/client_api/rm_common_libs.git/v2/library/errors"
	"rm.git/client_api/rm_common_libs.git/v2/library/utils"

	"rongma.com/srms/models"
	"rongma.com/srms/modules/client"
	"rongma.com/srms/modules/logger"
	"rongma.com/srms/rmgrpc/cloudquery"
)

func Response(ctx *gin.Context, data interface{}) {
	ctx.Header("Content-Type", "application/json")
	ctx.IndentedJSON(http.StatusOK, misc.NewResponse(data))
}

// CheckBodyContent 检查请求体内容
func CheckBodyContent(ctx *gin.Context) ([]byte, error) {

	var content []byte
	var err error

	if ctx.Request.ContentLength == -1 {
		return nil, errors.ContentLengthRequired
	}

	if content, err = io.ReadAll(ctx.Request.Body); err != nil {
		return nil, err
	}

	if (int64)(len(content)) != ctx.Request.ContentLength {
		return nil, errors.ContentLengthNotMatch
	}

	return content, nil

}

// EncryptResponse 加密返回结果
func EncryptResponse(data []byte, encryptKey string) (string, error) {
	var err error
	secretKey, err := utils.Base64Decode(encryptKey)
	if err != nil {
		return "", err
	}
	suite, _ := cipher.GetSuite(cipher.TLS_RSA_2048_WITH_AES_128_ECB_SHA256)
	encryptResult, err := suite.Aes.Encrypt(data, secretKey, nil)
	if err != nil {
		return "", err
	}
	return utils.Base64Encode(encryptResult), nil
}

// FormatBodyContent 根据请求参数类型过滤，将内容匹配到参数结构中
func FormatBodyContent(ctx *gin.Context, bodyContent []byte) ([]byte, error) {

	dataType := ctx.GetHeader("req_data_type")
	if dataType == "json" { // 请求为json数据
		return bodyContent, nil
	}

	params := strings.Split(string(bodyContent), "&")
	returnParams := make(map[string]string, len(params))
	for _, param := range params {
		key, value, _ := strings.Cut(param, "=")
		if key != "" {
			if key == "Md5" || key == "Sha1" || key == "CommandLineFileMd5" || key == "CommandLineFileSha1" { // md5,sha1的值统一为小写
				returnParams[key] = strings.ToLower(value)
			} else {
				returnParams[key] = value
			}
		}
	}

	jsonString, err := json.Marshal(returnParams)
	if err != nil {
		logger.WithContext(ctx).Errorf("json marshal error: %s", err)
		return nil, err
	}

	return jsonString, nil

}

// FormatBodyContentToMap 根据请求参数类型过滤，将内容匹配到参数结构中
//func FormatBodyContentToMap(ctx *gin.Context, bodyContent []byte) (map[string]string, error) {
//	returnParams := make(map[string]string)
//	parseParams, _ := url.ParseQuery(string(bodyContent))
//	for key1, val1 := range parseParams {
//		returnParams[strings.ToLower(key1)] = strings.ToLower(val1[0])
//	}
//	return returnParams, nil
//}

// func ZlibCompress(src []byte) []byte {
// 	var in bytes.Buffer
// 	w := zlib.NewWriter(&in)
// 	_, _ = w.Write(src)
// 	_ = w.Close()
// 	return in.Bytes()
// }

func ZlibUnCompress(compressSrc string) ([]byte, error) {
	b := bytes.NewReader([]byte(compressSrc))
	var out bytes.Buffer
	r, err := zlib.NewReader(b)
	if err != nil {
		return nil, err
	}
	io.Copy(&out, r)
	return out.Bytes(), nil
}

func GetCloudQueryLevel(ctx context.Context, req map[string]string, clientId string) (map[string]interface{}, error) {

	out := map[string]interface{}{}
	v, _ := json.Marshal(req)

	resp, err := client.CloudQuery.DoQuery(ctx, &cloudquery.DoQueryRequest{Query: string(v)})
	if err != nil {
		log.Errorf("err:%s", err)
		return out, err
	}

	if resp.GetDatas() != "null" {
		logger.WithContext(ctx).Infof("cloud query result, client: %v, tick: %v, resp: %v", clientId, req["i_tick"], resp)

		res := models.CloudQueryResponse{}
		err = json.Unmarshal([]byte(resp.GetDatas()), &res)
		if err != nil {
			return out, err
		}

		out = res.Out
	}

	return out, nil
}

func WriteToKafka(msg *models.KafkaMsg) error {

	_, err := client.KafkaWriter.Write([]*models.KafkaMsg{msg})
	return err
}
