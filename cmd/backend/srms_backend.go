package backend

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"rm.git/client_api/rm_common_libs.git/v2/library/cipher"
	"rm.git/client_api/rm_common_libs.git/v2/library/log"

	"rongma.com/srms/config"
	"rongma.com/srms/controllers"
	"rongma.com/srms/controllers/mac"
	v2 "rongma.com/srms/controllers/v2"
	v4 "rongma.com/srms/controllers/v4"
	"rongma.com/srms/middleware"
	"rongma.com/srms/modules/client"
	"rongma.com/srms/services"
)

var (
	conf = config.Config()
)

type SampleRepositoryManagementServer struct {
	engine  *gin.Engine
	server  *http.Server
	forever chan os.Signal
	events  *services.EventBusService
}

func NewSampleRepositoryManagementServer() *SampleRepositoryManagementServer {
	gin.SetMode(conf.Service.Mode)

	server := &SampleRepositoryManagementServer{}
	server.engine = gin.Default()
	server.engine.Use(middleware.Cors)
	server.events = services.NewEventBusService()
	server.server = &http.Server{Addr: conf.Service.Addr, Handler: server.engine}
	server.forever = make(chan os.Signal, 1)

	signal.Notify(server.forever, syscall.SIGINT, syscall.SIGTERM)
	return server
}

func (s *SampleRepositoryManagementServer) Init() {
	log.Init(log.NewConfig(conf.Logging))
	cipher.Init(conf.Certs)
	client.Init()

	s.initRouters()
}

func (s *SampleRepositoryManagementServer) initRouters() {

	if conf.TokenBucket.Rate > 0 && conf.TokenBucket.Capacity > 0 {
		tokenBucket := middleware.NewTokenBucket(client.ActLogger)
		s.engine.Use(tokenBucket.TokenBucket)
	}

	group := s.engine.Group("/cloud/files")

	sRmsController := controllers.NewSRMSController(services.NewSRMSService())
	{
		///v1/query/security中need_upload值设为0,则/v1/upload，/v1/upload/shards 不会调用
		group.POST("/v1/query/security", sRmsController.OnQueryFileSecurity)
		group.POST("/v1/upload", sRmsController.OnUpload)
		group.POST("/v1/upload/shards", sRmsController.OnUploadShard)
		//group.POST("/v1/upload/grpc", sRmsController.OnGrpc)
		group.POST("/v1/dump/upload", sRmsController.OnDumpUpload)
		group.POST("/v1/dump/config", sRmsController.OnGetDumpConfig)
		group.POST("/v1/upload/config", sRmsController.OnGetUploadConfig)
		group.POST("/v1/quarantine/inform", sRmsController.OnQuarantineFileInform)
		group.POST("/v1/trust/inform", sRmsController.OnTrustFileInform)

	}

	sRmsControllerV2 := v2.NewSRMSControllerV2(services.NewSRMSService(), services.NewIsolateFileService())
	{
		group.POST("/v2/query/security", sRmsControllerV2.OnQueryFileSecurity)
		group.POST("/v2/batch_query/security", sRmsControllerV2.OnBatchQueryFileSecurity) // 批量查询安全等级
		group.POST("/v2/upload", sRmsControllerV2.OnUpload)
		group.POST("/v2/upload/shards", sRmsControllerV2.OnUploadShard)
		group.POST("/v2/dump/upload", sRmsControllerV2.OnDumpUpload)
		group.POST("/v2/dump/config", sRmsControllerV2.OnGetDumpConfig)
		group.POST("/v2/upload/config", sRmsControllerV2.OnGetUploadConfig)
		group.POST("/v2/quarantine/inform", sRmsControllerV2.OnQuarantineFileInform)
		group.POST("/v2/trust/inform", sRmsControllerV2.OnTrustFileInform)
		group.POST("/v3/query/security", sRmsControllerV2.OnQueryFileSecurity)            // 使用v2的接口
		group.POST("/v3/batch_query/security", sRmsControllerV2.OnBatchQueryFileSecurity) // 使用v2的接口
		group.POST("/v3/upload", sRmsControllerV2.OnUpload)                               // 使用v2的接口
		group.POST("/v3/upload/shards", sRmsControllerV2.OnUploadShard)                   // 使用v2的接口
	}
	// 单独对外提供https接口的API
	{
		group.POST("/srms/v1/query/security", sRmsControllerV2.OnQueryFile)
	}

	sRmsControllerV4 := v4.NewSRMSControllerV4(services.NewSRMSService(), services.NewIsolateFileService())
	{
		group.POST("/v4/query/security", sRmsControllerV4.OnQueryFileSecurity)
		group.POST("/v4/batch_query/security", sRmsControllerV4.OnBatchQueryFileSecurity) // 批量查询安全等级
		group.POST("/v4/upload", sRmsControllerV4.OnUpload)
		group.POST("/v4/upload/shards", sRmsControllerV4.OnUploadShard)
		group.POST("/v4/dump/upload", sRmsControllerV4.OnDumpUpload)
		group.POST("/v4/dump/config", sRmsControllerV4.OnGetDumpConfig)
		group.POST("/v4/upload/config", sRmsControllerV4.OnGetUploadConfig)
		group.POST("/v4/quarantine/inform", sRmsControllerV4.OnQuarantineFileInform)
		group.POST("/v4/trust/inform", sRmsControllerV4.OnTrustFileInform)
	}

	sRmsIsolateFile := v2.NewIsolateFileController(services.NewIsolateFileService())
	{
		group.POST("/v1/isolate_file/upload", sRmsIsolateFile.UploadIsolateFile)
	}

	sRmsIsolateFileV4 := v4.NewIsolateFileControllerV4(services.NewIsolateFileService())
	{
		group.POST("/v4/isolate_file/upload", sRmsIsolateFileV4.UploadIsolateFile)
		group.POST("/v4/r3/isolate_file/upload", sRmsIsolateFileV4.UploadIsolateFileR3)
	}

	// mac版本接口
	groupMac := s.engine.Group("/mac/srms/v1")
	sRmsControllerMac := mac.NewSRMSControllerMac(services.NewSRMSService(), services.NewIsolateFileService())
	{
		groupMac.POST("/query/security", sRmsControllerMac.OnQueryFileSecurity)
		//groupMac.POST("/batch_query/security", sRmsControllerMac.OnBatchQueryFileSecurity) // 批量查询安全等级
		groupMac.POST("/upload", sRmsControllerMac.OnUpload)
		groupMac.POST("/upload/shards", sRmsControllerMac.OnUploadShard)

	}
	sRmsIsolateFileMac := mac.NewIsolateFileControllerMac(services.NewIsolateFileService())
	{
		groupMac.POST("/isolate_file/upload", sRmsIsolateFileMac.UploadIsolateFile)
	}

	healthCheckController := controllers.NewHealthCheckController()
	{
		group.GET("/health", healthCheckController.OnHealthCheck)
		if conf.Service.FileQueryRank {
			group.GET("/query_file_rank", healthCheckController.OnGetFileQueryRank)
		}
	}
}

func (s *SampleRepositoryManagementServer) ServeForever() {
	go func() {
		if err := s.server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatalf("start srms service failed: %v", err)
		}
	}()

	fmt.Println("srms backend service starting...")
	fmt.Printf("service config %+v\n", conf.Service)

	s.join()

	s.stop()
}

func (s *SampleRepositoryManagementServer) join() {
	<-s.forever
}

func (s *SampleRepositoryManagementServer) stop() {
	fmt.Println("stopping srms backend service...")

	client.Close()

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)

	defer cancel()

	if err := s.server.Shutdown(ctx); err != nil {
		log.Errorf("srms backend service stop error: %v", err)
	}

	fmt.Println("srms backend service stopped.")
}
