[service]
name = 'rmedr_srms'
addr = '0.0.0.0:16001'
debug = true
encrypt = true

[private_keys]
rsa_2048 = 'certs/rsa2048.prvkey'
rsa_4096 = 'certs/rsa4096.prvkey'

[redis]
addr = '192.168.111.185:36379'
password = ''
db   = 0
queue = ''

[logging]
type = 'json'
dir = 'log'
level = 'info'
stdout = true

[grpc_md5]
addr = '192.168.111.31:38889'

[grpc_srms]
addr = '192.168.111.54:8080'

[grpc_cloud]
addr = '192.168.111.195:38890'

[grpc_portal]
addr = '192.168.111.195:38890'

[actlogger]
debug = true
actlog_index_name =

[uploads]
quantity = 10
interval_time = 10
api_interval_time = 10
