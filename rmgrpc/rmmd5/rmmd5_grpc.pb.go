// 进入项目根目录执行，下面语句，会在 common/rmgrpc/ 下生成 go_package 设置的包
// protoc --go_out=common/rmgrpc/ --go-grpc_out=common/rmgrpc/ configs/proto/rmmd5.proto

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.24.4
// source: configs/proto/rmmd5.proto

package rmmd5

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	GetMd5Service_GetMd5Info_FullMethodName      = "/rmmd5.GetMd5Service/GetMd5Info"
	GetMd5Service_BatchGetMd5Info_FullMethodName = "/rmmd5.GetMd5Service/BatchGetMd5Info"
	GetMd5Service_GetUrlInfo_FullMethodName      = "/rmmd5.GetMd5Service/GetUrlInfo"
	GetMd5Service_GetQaxUrlInfo_FullMethodName   = "/rmmd5.GetMd5Service/GetQaxUrlInfo"
)

// GetMd5ServiceClient is the client API for GetMd5Service service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GetMd5ServiceClient interface {
	// 单个请求配置
	GetMd5Info(ctx context.Context, in *GetMd5InfoRequest, opts ...grpc.CallOption) (*GetMd5InfoReply, error)
	BatchGetMd5Info(ctx context.Context, in *BatchGetMd5InfoRequest, opts ...grpc.CallOption) (*BatchGetMd5InfoReply, error)
	GetUrlInfo(ctx context.Context, in *UrlReqParam, opts ...grpc.CallOption) (*UrlInfo, error)
	GetQaxUrlInfo(ctx context.Context, in *UrlReqParam, opts ...grpc.CallOption) (*GetQaxUrlInfoRes, error)
}

type getMd5ServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGetMd5ServiceClient(cc grpc.ClientConnInterface) GetMd5ServiceClient {
	return &getMd5ServiceClient{cc}
}

func (c *getMd5ServiceClient) GetMd5Info(ctx context.Context, in *GetMd5InfoRequest, opts ...grpc.CallOption) (*GetMd5InfoReply, error) {
	out := new(GetMd5InfoReply)
	err := c.cc.Invoke(ctx, GetMd5Service_GetMd5Info_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *getMd5ServiceClient) BatchGetMd5Info(ctx context.Context, in *BatchGetMd5InfoRequest, opts ...grpc.CallOption) (*BatchGetMd5InfoReply, error) {
	out := new(BatchGetMd5InfoReply)
	err := c.cc.Invoke(ctx, GetMd5Service_BatchGetMd5Info_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *getMd5ServiceClient) GetUrlInfo(ctx context.Context, in *UrlReqParam, opts ...grpc.CallOption) (*UrlInfo, error) {
	out := new(UrlInfo)
	err := c.cc.Invoke(ctx, GetMd5Service_GetUrlInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *getMd5ServiceClient) GetQaxUrlInfo(ctx context.Context, in *UrlReqParam, opts ...grpc.CallOption) (*GetQaxUrlInfoRes, error) {
	out := new(GetQaxUrlInfoRes)
	err := c.cc.Invoke(ctx, GetMd5Service_GetQaxUrlInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GetMd5ServiceServer is the server API for GetMd5Service service.
// All implementations must embed UnimplementedGetMd5ServiceServer
// for forward compatibility
type GetMd5ServiceServer interface {
	// 单个请求配置
	GetMd5Info(context.Context, *GetMd5InfoRequest) (*GetMd5InfoReply, error)
	BatchGetMd5Info(context.Context, *BatchGetMd5InfoRequest) (*BatchGetMd5InfoReply, error)
	GetUrlInfo(context.Context, *UrlReqParam) (*UrlInfo, error)
	GetQaxUrlInfo(context.Context, *UrlReqParam) (*GetQaxUrlInfoRes, error)
	mustEmbedUnimplementedGetMd5ServiceServer()
}

// UnimplementedGetMd5ServiceServer must be embedded to have forward compatible implementations.
type UnimplementedGetMd5ServiceServer struct {
}

func (UnimplementedGetMd5ServiceServer) GetMd5Info(context.Context, *GetMd5InfoRequest) (*GetMd5InfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMd5Info not implemented")
}
func (UnimplementedGetMd5ServiceServer) BatchGetMd5Info(context.Context, *BatchGetMd5InfoRequest) (*BatchGetMd5InfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetMd5Info not implemented")
}
func (UnimplementedGetMd5ServiceServer) GetUrlInfo(context.Context, *UrlReqParam) (*UrlInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUrlInfo not implemented")
}
func (UnimplementedGetMd5ServiceServer) GetQaxUrlInfo(context.Context, *UrlReqParam) (*GetQaxUrlInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQaxUrlInfo not implemented")
}
func (UnimplementedGetMd5ServiceServer) mustEmbedUnimplementedGetMd5ServiceServer() {}

// UnsafeGetMd5ServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GetMd5ServiceServer will
// result in compilation errors.
type UnsafeGetMd5ServiceServer interface {
	mustEmbedUnimplementedGetMd5ServiceServer()
}

func RegisterGetMd5ServiceServer(s grpc.ServiceRegistrar, srv GetMd5ServiceServer) {
	s.RegisterService(&GetMd5Service_ServiceDesc, srv)
}

func _GetMd5Service_GetMd5Info_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMd5InfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GetMd5ServiceServer).GetMd5Info(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GetMd5Service_GetMd5Info_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GetMd5ServiceServer).GetMd5Info(ctx, req.(*GetMd5InfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GetMd5Service_BatchGetMd5Info_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetMd5InfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GetMd5ServiceServer).BatchGetMd5Info(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GetMd5Service_BatchGetMd5Info_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GetMd5ServiceServer).BatchGetMd5Info(ctx, req.(*BatchGetMd5InfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GetMd5Service_GetUrlInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UrlReqParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GetMd5ServiceServer).GetUrlInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GetMd5Service_GetUrlInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GetMd5ServiceServer).GetUrlInfo(ctx, req.(*UrlReqParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _GetMd5Service_GetQaxUrlInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UrlReqParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GetMd5ServiceServer).GetQaxUrlInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GetMd5Service_GetQaxUrlInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GetMd5ServiceServer).GetQaxUrlInfo(ctx, req.(*UrlReqParam))
	}
	return interceptor(ctx, in, info, handler)
}

// GetMd5Service_ServiceDesc is the grpc.ServiceDesc for GetMd5Service service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GetMd5Service_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "rmmd5.GetMd5Service",
	HandlerType: (*GetMd5ServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMd5Info",
			Handler:    _GetMd5Service_GetMd5Info_Handler,
		},
		{
			MethodName: "BatchGetMd5Info",
			Handler:    _GetMd5Service_BatchGetMd5Info_Handler,
		},
		{
			MethodName: "GetUrlInfo",
			Handler:    _GetMd5Service_GetUrlInfo_Handler,
		},
		{
			MethodName: "GetQaxUrlInfo",
			Handler:    _GetMd5Service_GetQaxUrlInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "configs/proto/rmmd5.proto",
}
