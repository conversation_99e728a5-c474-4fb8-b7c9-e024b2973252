// 进入项目根目录执行，下面语句，会在 common/rmgrpc/ 下生成 go_package 设置的包
// protoc --go_out=common/rmgrpc/ --go-grpc_out=common/rmgrpc/ configs/proto/rmmd5.proto

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.22.2
// source: configs/proto/rmmd5.proto

package rmmd5

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求主体结构
type RequestCon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5      string `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`
	Sha1     string `protobuf:"bytes,2,opt,name=sha1,proto3" json:"sha1,omitempty"`
	FileName string `protobuf:"bytes,3,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
}

func (x *RequestCon) Reset() {
	*x = RequestCon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_configs_proto_rmmd5_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestCon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestCon) ProtoMessage() {}

func (x *RequestCon) ProtoReflect() protoreflect.Message {
	mi := &file_configs_proto_rmmd5_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestCon.ProtoReflect.Descriptor instead.
func (*RequestCon) Descriptor() ([]byte, []int) {
	return file_configs_proto_rmmd5_proto_rawDescGZIP(), []int{0}
}

func (x *RequestCon) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *RequestCon) GetSha1() string {
	if x != nil {
		return x.Sha1
	}
	return ""
}

func (x *RequestCon) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type GetMd5InfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestCon *RequestCon `protobuf:"bytes,1,opt,name=requestCon,proto3" json:"requestCon,omitempty"`
	IsDirect   bool        `protobuf:"varint,2,opt,name=is_direct,json=isDirect,proto3" json:"is_direct,omitempty"`
}

func (x *GetMd5InfoRequest) Reset() {
	*x = GetMd5InfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_configs_proto_rmmd5_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMd5InfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMd5InfoRequest) ProtoMessage() {}

func (x *GetMd5InfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configs_proto_rmmd5_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMd5InfoRequest.ProtoReflect.Descriptor instead.
func (*GetMd5InfoRequest) Descriptor() ([]byte, []int) {
	return file_configs_proto_rmmd5_proto_rawDescGZIP(), []int{1}
}

func (x *GetMd5InfoRequest) GetRequestCon() *RequestCon {
	if x != nil {
		return x.RequestCon
	}
	return nil
}

func (x *GetMd5InfoRequest) GetIsDirect() bool {
	if x != nil {
		return x.IsDirect
	}
	return false
}

type BatchGetMd5InfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestCon []*RequestCon `protobuf:"bytes,1,rep,name=requestCon,proto3" json:"requestCon,omitempty"`
	IsDirect   bool          `protobuf:"varint,2,opt,name=is_direct,json=isDirect,proto3" json:"is_direct,omitempty"`
}

func (x *BatchGetMd5InfoRequest) Reset() {
	*x = BatchGetMd5InfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_configs_proto_rmmd5_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetMd5InfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetMd5InfoRequest) ProtoMessage() {}

func (x *BatchGetMd5InfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configs_proto_rmmd5_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetMd5InfoRequest.ProtoReflect.Descriptor instead.
func (*BatchGetMd5InfoRequest) Descriptor() ([]byte, []int) {
	return file_configs_proto_rmmd5_proto_rawDescGZIP(), []int{2}
}

func (x *BatchGetMd5InfoRequest) GetRequestCon() []*RequestCon {
	if x != nil {
		return x.RequestCon
	}
	return nil
}

func (x *BatchGetMd5InfoRequest) GetIsDirect() bool {
	if x != nil {
		return x.IsDirect
	}
	return false
}

type GetMd5InfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code uint64                `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string                `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data *GetMd5InfoReplyDatas `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetMd5InfoReply) Reset() {
	*x = GetMd5InfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_configs_proto_rmmd5_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMd5InfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMd5InfoReply) ProtoMessage() {}

func (x *GetMd5InfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_configs_proto_rmmd5_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMd5InfoReply.ProtoReflect.Descriptor instead.
func (*GetMd5InfoReply) Descriptor() ([]byte, []int) {
	return file_configs_proto_rmmd5_proto_rawDescGZIP(), []int{3}
}

func (x *GetMd5InfoReply) GetCode() uint64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetMd5InfoReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetMd5InfoReply) GetData() *GetMd5InfoReplyDatas {
	if x != nil {
		return x.Data
	}
	return nil
}

type BatchGetMd5InfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code uint64                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data []*GetMd5InfoReplyDatas `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *BatchGetMd5InfoReply) Reset() {
	*x = BatchGetMd5InfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_configs_proto_rmmd5_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetMd5InfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetMd5InfoReply) ProtoMessage() {}

func (x *BatchGetMd5InfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_configs_proto_rmmd5_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetMd5InfoReply.ProtoReflect.Descriptor instead.
func (*BatchGetMd5InfoReply) Descriptor() ([]byte, []int) {
	return file_configs_proto_rmmd5_proto_rawDescGZIP(), []int{4}
}

func (x *BatchGetMd5InfoReply) GetCode() uint64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BatchGetMd5InfoReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BatchGetMd5InfoReply) GetData() []*GetMd5InfoReplyDatas {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetMd5InfoReplyDatas struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version  uint64                          `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	Security *GetMd5InfoReplyDatasSecurities `protobuf:"bytes,2,opt,name=security,proto3" json:"security,omitempty"`
}

func (x *GetMd5InfoReplyDatas) Reset() {
	*x = GetMd5InfoReplyDatas{}
	if protoimpl.UnsafeEnabled {
		mi := &file_configs_proto_rmmd5_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMd5InfoReplyDatas) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMd5InfoReplyDatas) ProtoMessage() {}

func (x *GetMd5InfoReplyDatas) ProtoReflect() protoreflect.Message {
	mi := &file_configs_proto_rmmd5_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMd5InfoReplyDatas.ProtoReflect.Descriptor instead.
func (*GetMd5InfoReplyDatas) Descriptor() ([]byte, []int) {
	return file_configs_proto_rmmd5_proto_rawDescGZIP(), []int{3, 0}
}

func (x *GetMd5InfoReplyDatas) GetVersion() uint64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *GetMd5InfoReplyDatas) GetSecurity() *GetMd5InfoReplyDatasSecurities {
	if x != nil {
		return x.Security
	}
	return nil
}

type GetMd5InfoReplyDatasSecurities struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level        uint32                                      `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Sublevel     uint32                                      `protobuf:"varint,2,opt,name=sublevel,proto3" json:"sublevel,omitempty"`
	Src          uint32                                      `protobuf:"varint,3,opt,name=src,proto3" json:"src,omitempty"`
	WhiteChannel uint32                                      `protobuf:"varint,4,opt,name=white_channel,json=whiteChannel,proto3" json:"white_channel,omitempty"`
	ReadOnly     bool                                        `protobuf:"varint,5,opt,name=read_only,json=readOnly,proto3" json:"read_only,omitempty"`
	Popularity   uint32                                      `protobuf:"varint,6,opt,name=popularity,proto3" json:"popularity,omitempty"`
	Age          uint64                                      `protobuf:"varint,7,opt,name=age,proto3" json:"age,omitempty"`
	Description  []byte                                      `protobuf:"bytes,8,opt,name=description,proto3" json:"description,omitempty"`
	HasMalware   *GetMd5InfoReplyDatasSecurities_HasMalwares `protobuf:"bytes,9,opt,name=HasMalware,proto3" json:"HasMalware,omitempty"`
	CreateTime   *GetMd5InfoReplyDatasSecuritiesTimes        `protobuf:"bytes,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime   *GetMd5InfoReplyDatasSecuritiesTimes        `protobuf:"bytes,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Extend       string                                      `protobuf:"bytes,12,opt,name=extend,proto3" json:"extend,omitempty"`
}

func (x *GetMd5InfoReplyDatasSecurities) Reset() {
	*x = GetMd5InfoReplyDatasSecurities{}
	if protoimpl.UnsafeEnabled {
		mi := &file_configs_proto_rmmd5_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMd5InfoReplyDatasSecurities) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMd5InfoReplyDatasSecurities) ProtoMessage() {}

func (x *GetMd5InfoReplyDatasSecurities) ProtoReflect() protoreflect.Message {
	mi := &file_configs_proto_rmmd5_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMd5InfoReplyDatasSecurities.ProtoReflect.Descriptor instead.
func (*GetMd5InfoReplyDatasSecurities) Descriptor() ([]byte, []int) {
	return file_configs_proto_rmmd5_proto_rawDescGZIP(), []int{3, 0, 0}
}

func (x *GetMd5InfoReplyDatasSecurities) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *GetMd5InfoReplyDatasSecurities) GetSublevel() uint32 {
	if x != nil {
		return x.Sublevel
	}
	return 0
}

func (x *GetMd5InfoReplyDatasSecurities) GetSrc() uint32 {
	if x != nil {
		return x.Src
	}
	return 0
}

func (x *GetMd5InfoReplyDatasSecurities) GetWhiteChannel() uint32 {
	if x != nil {
		return x.WhiteChannel
	}
	return 0
}

func (x *GetMd5InfoReplyDatasSecurities) GetReadOnly() bool {
	if x != nil {
		return x.ReadOnly
	}
	return false
}

func (x *GetMd5InfoReplyDatasSecurities) GetPopularity() uint32 {
	if x != nil {
		return x.Popularity
	}
	return 0
}

func (x *GetMd5InfoReplyDatasSecurities) GetAge() uint64 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *GetMd5InfoReplyDatasSecurities) GetDescription() []byte {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *GetMd5InfoReplyDatasSecurities) GetHasMalware() *GetMd5InfoReplyDatasSecurities_HasMalwares {
	if x != nil {
		return x.HasMalware
	}
	return nil
}

func (x *GetMd5InfoReplyDatasSecurities) GetCreateTime() *GetMd5InfoReplyDatasSecuritiesTimes {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *GetMd5InfoReplyDatasSecurities) GetUpdateTime() *GetMd5InfoReplyDatasSecuritiesTimes {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *GetMd5InfoReplyDatasSecurities) GetExtend() string {
	if x != nil {
		return x.Extend
	}
	return ""
}

type GetMd5InfoReplyDatasSecurities_HasMalwares struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MalwareId *GetMd5InfoReplyDatasSecurities_HasMalwares_MalwareId `protobuf:"bytes,1,opt,name=malware_id,json=malwareId,proto3" json:"malware_id,omitempty"`
	Malware   *GetMd5InfoReplyDatasSecurities_HasMalwaresMalwares   `protobuf:"bytes,2,opt,name=malware,proto3" json:"malware,omitempty"`
}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwares) Reset() {
	*x = GetMd5InfoReplyDatasSecurities_HasMalwares{}
	if protoimpl.UnsafeEnabled {
		mi := &file_configs_proto_rmmd5_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwares) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMd5InfoReplyDatasSecurities_HasMalwares) ProtoMessage() {}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwares) ProtoReflect() protoreflect.Message {
	mi := &file_configs_proto_rmmd5_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMd5InfoReplyDatasSecurities_HasMalwares.ProtoReflect.Descriptor instead.
func (*GetMd5InfoReplyDatasSecurities_HasMalwares) Descriptor() ([]byte, []int) {
	return file_configs_proto_rmmd5_proto_rawDescGZIP(), []int{3, 0, 0, 0}
}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwares) GetMalwareId() *GetMd5InfoReplyDatasSecurities_HasMalwares_MalwareId {
	if x != nil {
		return x.MalwareId
	}
	return nil
}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwares) GetMalware() *GetMd5InfoReplyDatasSecurities_HasMalwaresMalwares {
	if x != nil {
		return x.Malware
	}
	return nil
}

type GetMd5InfoReplyDatasSecuritiesTimes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Seconds int64 `protobuf:"varint,1,opt,name=seconds,proto3" json:"seconds,omitempty"`
	Nanos   int32 `protobuf:"varint,2,opt,name=nanos,proto3" json:"nanos,omitempty"`
}

func (x *GetMd5InfoReplyDatasSecuritiesTimes) Reset() {
	*x = GetMd5InfoReplyDatasSecuritiesTimes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_configs_proto_rmmd5_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMd5InfoReplyDatasSecuritiesTimes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMd5InfoReplyDatasSecuritiesTimes) ProtoMessage() {}

func (x *GetMd5InfoReplyDatasSecuritiesTimes) ProtoReflect() protoreflect.Message {
	mi := &file_configs_proto_rmmd5_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMd5InfoReplyDatasSecuritiesTimes.ProtoReflect.Descriptor instead.
func (*GetMd5InfoReplyDatasSecuritiesTimes) Descriptor() ([]byte, []int) {
	return file_configs_proto_rmmd5_proto_rawDescGZIP(), []int{3, 0, 0, 1}
}

func (x *GetMd5InfoReplyDatasSecuritiesTimes) GetSeconds() int64 {
	if x != nil {
		return x.Seconds
	}
	return 0
}

func (x *GetMd5InfoReplyDatasSecuritiesTimes) GetNanos() int32 {
	if x != nil {
		return x.Nanos
	}
	return 0
}

type GetMd5InfoReplyDatasSecurities_HasMalwaresMalwares struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Class string `protobuf:"bytes,2,opt,name=class,proto3" json:"class,omitempty"`
}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwaresMalwares) Reset() {
	*x = GetMd5InfoReplyDatasSecurities_HasMalwaresMalwares{}
	if protoimpl.UnsafeEnabled {
		mi := &file_configs_proto_rmmd5_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwaresMalwares) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMd5InfoReplyDatasSecurities_HasMalwaresMalwares) ProtoMessage() {}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwaresMalwares) ProtoReflect() protoreflect.Message {
	mi := &file_configs_proto_rmmd5_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMd5InfoReplyDatasSecurities_HasMalwaresMalwares.ProtoReflect.Descriptor instead.
func (*GetMd5InfoReplyDatasSecurities_HasMalwaresMalwares) Descriptor() ([]byte, []int) {
	return file_configs_proto_rmmd5_proto_rawDescGZIP(), []int{3, 0, 0, 0, 0}
}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwaresMalwares) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwaresMalwares) GetClass() string {
	if x != nil {
		return x.Class
	}
	return ""
}

type GetMd5InfoReplyDatasSecurities_HasMalwares_MalwareId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mid uint32 `protobuf:"varint,1,opt,name=mid,proto3" json:"mid,omitempty"`
	Cid uint32 `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwares_MalwareId) Reset() {
	*x = GetMd5InfoReplyDatasSecurities_HasMalwares_MalwareId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_configs_proto_rmmd5_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwares_MalwareId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMd5InfoReplyDatasSecurities_HasMalwares_MalwareId) ProtoMessage() {}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwares_MalwareId) ProtoReflect() protoreflect.Message {
	mi := &file_configs_proto_rmmd5_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMd5InfoReplyDatasSecurities_HasMalwares_MalwareId.ProtoReflect.Descriptor instead.
func (*GetMd5InfoReplyDatasSecurities_HasMalwares_MalwareId) Descriptor() ([]byte, []int) {
	return file_configs_proto_rmmd5_proto_rawDescGZIP(), []int{3, 0, 0, 0, 1}
}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwares_MalwareId) GetMid() uint32 {
	if x != nil {
		return x.Mid
	}
	return 0
}

func (x *GetMd5InfoReplyDatasSecurities_HasMalwares_MalwareId) GetCid() uint32 {
	if x != nil {
		return x.Cid
	}
	return 0
}

var File_configs_proto_rmmd5_proto protoreflect.FileDescriptor

var file_configs_proto_rmmd5_proto_rawDesc = []byte{
	0x0a, 0x19, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x72, 0x6d, 0x6d, 0x64, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x72, 0x6d, 0x6d,
	0x64, 0x35, 0x22, 0x4f, 0x0a, 0x0a, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x68, 0x61, 0x31, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x73, 0x68, 0x61, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x63, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4d, 0x64, 0x35, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x72,
	0x6d, 0x6d, 0x64, 0x35, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x52,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x69,
	0x73, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x69, 0x73, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x22, 0x68, 0x0a, 0x16, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x65, 0x74, 0x4d, 0x64, 0x35, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x72, 0x6d, 0x6d, 0x64, 0x35, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x43, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x44, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x22, 0xae, 0x08, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4d, 0x64, 0x35, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x30, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x72, 0x6d, 0x6d,
	0x64, 0x35, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x64, 0x35, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0xc2,
	0x07, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x61, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x08, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x6d, 0x6d, 0x64, 0x35, 0x2e, 0x47, 0x65, 0x74,
	0x4d, 0x64, 0x35, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x08, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x1a, 0xd9, 0x06, 0x0a, 0x0a, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08,
	0x73, 0x75, 0x62, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x73, 0x75, 0x62, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x72, 0x63, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73, 0x72, 0x63, 0x12, 0x23, 0x0a, 0x0d, 0x77, 0x68,
	0x69, 0x74, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0c, 0x77, 0x68, 0x69, 0x74, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x1e, 0x0a, 0x0a,
	0x70, 0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x70, 0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x53, 0x0a, 0x0a, 0x48, 0x61, 0x73, 0x4d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x72, 0x6d, 0x6d, 0x64, 0x35, 0x2e, 0x47, 0x65, 0x74,
	0x4d, 0x64, 0x35, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x48, 0x61,
	0x73, 0x4d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65, 0x73, 0x52, 0x0a, 0x48, 0x61, 0x73, 0x4d, 0x61,
	0x6c, 0x77, 0x61, 0x72, 0x65, 0x12, 0x4e, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x72, 0x6d, 0x6d,
	0x64, 0x35, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x64, 0x35, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4e, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x72, 0x6d, 0x6d,
	0x64, 0x35, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x64, 0x35, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x1a, 0xaa, 0x02,
	0x0a, 0x0b, 0x48, 0x61, 0x73, 0x4d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65, 0x73, 0x12, 0x5c, 0x0a,
	0x0a, 0x6d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3d, 0x2e, 0x72, 0x6d, 0x6d, 0x64, 0x35, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x64, 0x35,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x2e,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x48, 0x61, 0x73, 0x4d, 0x61,
	0x6c, 0x77, 0x61, 0x72, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65, 0x49, 0x64,
	0x52, 0x09, 0x6d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65, 0x49, 0x64, 0x12, 0x56, 0x0a, 0x07, 0x6d,
	0x61, 0x6c, 0x77, 0x61, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x72,
	0x6d, 0x6d, 0x64, 0x35, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x64, 0x35, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x48, 0x61, 0x73, 0x4d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65,
	0x73, 0x2e, 0x6d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65, 0x73, 0x52, 0x07, 0x6d, 0x61, 0x6c, 0x77,
	0x61, 0x72, 0x65, 0x1a, 0x34, 0x0a, 0x08, 0x6d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x1a, 0x2f, 0x0a, 0x09, 0x4d, 0x61, 0x6c,
	0x77, 0x61, 0x72, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x6d, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x63, 0x69, 0x64, 0x1a, 0x37, 0x0a, 0x05, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x6e, 0x61, 0x6e, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6e, 0x61,
	0x6e, 0x6f, 0x73, 0x22, 0x6e, 0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4d,
	0x64, 0x35, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x72, 0x6d, 0x6d, 0x64, 0x35, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x64, 0x35, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x32, 0xa2, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4d, 0x64, 0x35, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x40, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x4d, 0x64, 0x35, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x18, 0x2e, 0x72, 0x6d, 0x6d, 0x64, 0x35, 0x2e, 0x47, 0x65, 0x74, 0x4d,
	0x64, 0x35, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x72, 0x6d, 0x6d, 0x64, 0x35, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x64, 0x35, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x4f, 0x0a, 0x0f, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x47, 0x65, 0x74, 0x4d, 0x64, 0x35, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x2e, 0x72, 0x6d, 0x6d,
	0x64, 0x35, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4d, 0x64, 0x35, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x72, 0x6d, 0x6d, 0x64,
	0x35, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4d, 0x64, 0x35, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x42, 0x09, 0x5a, 0x07, 0x2e, 0x2f, 0x72, 0x6d,
	0x6d, 0x64, 0x35, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_configs_proto_rmmd5_proto_rawDescOnce sync.Once
	file_configs_proto_rmmd5_proto_rawDescData = file_configs_proto_rmmd5_proto_rawDesc
)

func file_configs_proto_rmmd5_proto_rawDescGZIP() []byte {
	file_configs_proto_rmmd5_proto_rawDescOnce.Do(func() {
		file_configs_proto_rmmd5_proto_rawDescData = protoimpl.X.CompressGZIP(file_configs_proto_rmmd5_proto_rawDescData)
	})
	return file_configs_proto_rmmd5_proto_rawDescData
}

var file_configs_proto_rmmd5_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_configs_proto_rmmd5_proto_goTypes = []interface{}{
	(*RequestCon)(nil),                                           // 0: rmmd5.RequestCon
	(*GetMd5InfoRequest)(nil),                                    // 1: rmmd5.GetMd5InfoRequest
	(*BatchGetMd5InfoRequest)(nil),                               // 2: rmmd5.BatchGetMd5InfoRequest
	(*GetMd5InfoReply)(nil),                                      // 3: rmmd5.GetMd5InfoReply
	(*BatchGetMd5InfoReply)(nil),                                 // 4: rmmd5.BatchGetMd5InfoReply
	(*GetMd5InfoReplyDatas)(nil),                                 // 5: rmmd5.GetMd5InfoReply.datas
	(*GetMd5InfoReplyDatasSecurities)(nil),                       // 6: rmmd5.GetMd5InfoReply.datas.securities
	(*GetMd5InfoReplyDatasSecurities_HasMalwares)(nil),           // 7: rmmd5.GetMd5InfoReply.datas.securities.HasMalwares
	(*GetMd5InfoReplyDatasSecuritiesTimes)(nil),                  // 8: rmmd5.GetMd5InfoReply.datas.securities.times
	(*GetMd5InfoReplyDatasSecurities_HasMalwaresMalwares)(nil),   // 9: rmmd5.GetMd5InfoReply.datas.securities.HasMalwares.malwares
	(*GetMd5InfoReplyDatasSecurities_HasMalwares_MalwareId)(nil), // 10: rmmd5.GetMd5InfoReply.datas.securities.HasMalwares.MalwareId
}
var file_configs_proto_rmmd5_proto_depIdxs = []int32{
	0,  // 0: rmmd5.GetMd5InfoRequest.requestCon:type_name -> rmmd5.RequestCon
	0,  // 1: rmmd5.BatchGetMd5InfoRequest.requestCon:type_name -> rmmd5.RequestCon
	5,  // 2: rmmd5.GetMd5InfoReply.data:type_name -> rmmd5.GetMd5InfoReply.datas
	5,  // 3: rmmd5.BatchGetMd5InfoReply.data:type_name -> rmmd5.GetMd5InfoReply.datas
	6,  // 4: rmmd5.GetMd5InfoReply.datas.security:type_name -> rmmd5.GetMd5InfoReply.datas.securities
	7,  // 5: rmmd5.GetMd5InfoReply.datas.securities.HasMalware:type_name -> rmmd5.GetMd5InfoReply.datas.securities.HasMalwares
	8,  // 6: rmmd5.GetMd5InfoReply.datas.securities.create_time:type_name -> rmmd5.GetMd5InfoReply.datas.securities.times
	8,  // 7: rmmd5.GetMd5InfoReply.datas.securities.update_time:type_name -> rmmd5.GetMd5InfoReply.datas.securities.times
	10, // 8: rmmd5.GetMd5InfoReply.datas.securities.HasMalwares.malware_id:type_name -> rmmd5.GetMd5InfoReply.datas.securities.HasMalwares.MalwareId
	9,  // 9: rmmd5.GetMd5InfoReply.datas.securities.HasMalwares.malware:type_name -> rmmd5.GetMd5InfoReply.datas.securities.HasMalwares.malwares
	1,  // 10: rmmd5.GetMd5Service.GetMd5Info:input_type -> rmmd5.GetMd5InfoRequest
	2,  // 11: rmmd5.GetMd5Service.BatchGetMd5Info:input_type -> rmmd5.BatchGetMd5InfoRequest
	3,  // 12: rmmd5.GetMd5Service.GetMd5Info:output_type -> rmmd5.GetMd5InfoReply
	4,  // 13: rmmd5.GetMd5Service.BatchGetMd5Info:output_type -> rmmd5.BatchGetMd5InfoReply
	12, // [12:14] is the sub-list for method output_type
	10, // [10:12] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_configs_proto_rmmd5_proto_init() }
func file_configs_proto_rmmd5_proto_init() {
	if File_configs_proto_rmmd5_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_configs_proto_rmmd5_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestCon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_configs_proto_rmmd5_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMd5InfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_configs_proto_rmmd5_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetMd5InfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_configs_proto_rmmd5_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMd5InfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_configs_proto_rmmd5_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetMd5InfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_configs_proto_rmmd5_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMd5InfoReplyDatas); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_configs_proto_rmmd5_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMd5InfoReplyDatasSecurities); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_configs_proto_rmmd5_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMd5InfoReplyDatasSecurities_HasMalwares); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_configs_proto_rmmd5_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMd5InfoReplyDatasSecuritiesTimes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_configs_proto_rmmd5_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMd5InfoReplyDatasSecurities_HasMalwaresMalwares); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_configs_proto_rmmd5_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMd5InfoReplyDatasSecurities_HasMalwares_MalwareId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_configs_proto_rmmd5_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_configs_proto_rmmd5_proto_goTypes,
		DependencyIndexes: file_configs_proto_rmmd5_proto_depIdxs,
		MessageInfos:      file_configs_proto_rmmd5_proto_msgTypes,
	}.Build()
	File_configs_proto_rmmd5_proto = out.File
	file_configs_proto_rmmd5_proto_rawDesc = nil
	file_configs_proto_rmmd5_proto_goTypes = nil
	file_configs_proto_rmmd5_proto_depIdxs = nil
}
