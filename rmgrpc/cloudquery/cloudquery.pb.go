// 进入项目根目录执行，下面语句，会在 common/rmgrpc/ 下生成 go_package 设置的包
// protoc --go_out=common/rmgrpc/ --go-grpc_out=common/rmgrpc/ configs/proto/rmmd5.proto

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.29.0
// 	protoc        v3.21.5
// source: rmgrpc/cloudquery/proto/cloudquery.proto

package cloudquery

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求主体结构
type DoQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query string `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
}

func (x *DoQueryRequest) Reset() {
	*x = DoQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rmgrpc_cloudquery_proto_cloudquery_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoQueryRequest) ProtoMessage() {}

func (x *DoQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rmgrpc_cloudquery_proto_cloudquery_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoQueryRequest.ProtoReflect.Descriptor instead.
func (*DoQueryRequest) Descriptor() ([]byte, []int) {
	return file_rmgrpc_cloudquery_proto_cloudquery_proto_rawDescGZIP(), []int{0}
}

func (x *DoQueryRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

type DoQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  uint64 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Datas string `protobuf:"bytes,3,opt,name=datas,proto3" json:"datas,omitempty"`
}

func (x *DoQueryResponse) Reset() {
	*x = DoQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rmgrpc_cloudquery_proto_cloudquery_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoQueryResponse) ProtoMessage() {}

func (x *DoQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rmgrpc_cloudquery_proto_cloudquery_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoQueryResponse.ProtoReflect.Descriptor instead.
func (*DoQueryResponse) Descriptor() ([]byte, []int) {
	return file_rmgrpc_cloudquery_proto_cloudquery_proto_rawDescGZIP(), []int{1}
}

func (x *DoQueryResponse) GetCode() uint64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DoQueryResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoQueryResponse) GetDatas() string {
	if x != nil {
		return x.Datas
	}
	return ""
}

var File_rmgrpc_cloudquery_proto_cloudquery_proto protoreflect.FileDescriptor

var file_rmgrpc_cloudquery_proto_cloudquery_proto_rawDesc = []byte{
	0x0a, 0x28, 0x72, 0x6d, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x71, 0x75,
	0x65, 0x72, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0x26, 0x0a, 0x0e, 0x44, 0x6f, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0x4d,
	0x0a, 0x0f, 0x44, 0x6f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x61, 0x74, 0x61, 0x73, 0x32, 0x4f, 0x0a,
	0x07, 0x44, 0x6f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x44, 0x0a, 0x07, 0x44, 0x6f, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x2e, 0x44, 0x6f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x71, 0x75, 0x65, 0x72, 0x79, 0x2e, 0x44, 0x6f, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x0e,
	0x5a, 0x0c, 0x2e, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x71, 0x75, 0x65, 0x72, 0x79, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_rmgrpc_cloudquery_proto_cloudquery_proto_rawDescOnce sync.Once
	file_rmgrpc_cloudquery_proto_cloudquery_proto_rawDescData = file_rmgrpc_cloudquery_proto_cloudquery_proto_rawDesc
)

func file_rmgrpc_cloudquery_proto_cloudquery_proto_rawDescGZIP() []byte {
	file_rmgrpc_cloudquery_proto_cloudquery_proto_rawDescOnce.Do(func() {
		file_rmgrpc_cloudquery_proto_cloudquery_proto_rawDescData = protoimpl.X.CompressGZIP(file_rmgrpc_cloudquery_proto_cloudquery_proto_rawDescData)
	})
	return file_rmgrpc_cloudquery_proto_cloudquery_proto_rawDescData
}

var file_rmgrpc_cloudquery_proto_cloudquery_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_rmgrpc_cloudquery_proto_cloudquery_proto_goTypes = []interface{}{
	(*DoQueryRequest)(nil),  // 0: cloudquery.DoQueryRequest
	(*DoQueryResponse)(nil), // 1: cloudquery.DoQueryResponse
}
var file_rmgrpc_cloudquery_proto_cloudquery_proto_depIdxs = []int32{
	0, // 0: cloudquery.DoQuery.DoQuery:input_type -> cloudquery.DoQueryRequest
	1, // 1: cloudquery.DoQuery.DoQuery:output_type -> cloudquery.DoQueryResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rmgrpc_cloudquery_proto_cloudquery_proto_init() }
func file_rmgrpc_cloudquery_proto_cloudquery_proto_init() {
	if File_rmgrpc_cloudquery_proto_cloudquery_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_rmgrpc_cloudquery_proto_cloudquery_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rmgrpc_cloudquery_proto_cloudquery_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_rmgrpc_cloudquery_proto_cloudquery_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rmgrpc_cloudquery_proto_cloudquery_proto_goTypes,
		DependencyIndexes: file_rmgrpc_cloudquery_proto_cloudquery_proto_depIdxs,
		MessageInfos:      file_rmgrpc_cloudquery_proto_cloudquery_proto_msgTypes,
	}.Build()
	File_rmgrpc_cloudquery_proto_cloudquery_proto = out.File
	file_rmgrpc_cloudquery_proto_cloudquery_proto_rawDesc = nil
	file_rmgrpc_cloudquery_proto_cloudquery_proto_goTypes = nil
	file_rmgrpc_cloudquery_proto_cloudquery_proto_depIdxs = nil
}
