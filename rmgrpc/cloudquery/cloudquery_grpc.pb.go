// 进入项目根目录执行，下面语句，会在 common/rmgrpc/ 下生成 go_package 设置的包
// protoc --go_out=common/rmgrpc/ --go-grpc_out=common/rmgrpc/ configs/proto/rmmd5.proto

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.21.5
// source: rmgrpc/cloudquery/proto/cloudquery.proto

package cloudquery

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DoQuery_DoQuery_FullMethodName = "/cloudquery.DoQuery/DoQuery"
)

// DoQueryClient is the client API for DoQuery service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DoQueryClient interface {
	// 单个请求配置
	DoQuery(ctx context.Context, in *DoQueryRequest, opts ...grpc.CallOption) (*DoQueryResponse, error)
}

type doQueryClient struct {
	cc grpc.ClientConnInterface
}

func NewDoQueryClient(cc grpc.ClientConnInterface) DoQueryClient {
	return &doQueryClient{cc}
}

func (c *doQueryClient) DoQuery(ctx context.Context, in *DoQueryRequest, opts ...grpc.CallOption) (*DoQueryResponse, error) {
	out := new(DoQueryResponse)
	err := c.cc.Invoke(ctx, DoQuery_DoQuery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DoQueryServer is the server API for DoQuery service.
// All implementations must embed UnimplementedDoQueryServer
// for forward compatibility
type DoQueryServer interface {
	// 单个请求配置
	DoQuery(context.Context, *DoQueryRequest) (*DoQueryResponse, error)
	mustEmbedUnimplementedDoQueryServer()
}

// UnimplementedDoQueryServer must be embedded to have forward compatible implementations.
type UnimplementedDoQueryServer struct {
}

func (UnimplementedDoQueryServer) DoQuery(context.Context, *DoQueryRequest) (*DoQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoQuery not implemented")
}
func (UnimplementedDoQueryServer) mustEmbedUnimplementedDoQueryServer() {}

// UnsafeDoQueryServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DoQueryServer will
// result in compilation errors.
type UnsafeDoQueryServer interface {
	mustEmbedUnimplementedDoQueryServer()
}

func RegisterDoQueryServer(s grpc.ServiceRegistrar, srv DoQueryServer) {
	s.RegisterService(&DoQuery_ServiceDesc, srv)
}

func _DoQuery_DoQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoQueryServer).DoQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DoQuery_DoQuery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoQueryServer).DoQuery(ctx, req.(*DoQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DoQuery_ServiceDesc is the grpc.ServiceDesc for DoQuery service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DoQuery_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cloudquery.DoQuery",
	HandlerType: (*DoQueryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DoQuery",
			Handler:    _DoQuery_DoQuery_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rmgrpc/cloudquery/proto/cloudquery.proto",
}
