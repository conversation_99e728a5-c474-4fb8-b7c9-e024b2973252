package main

/* func main() {
	md5 := ""
	sha1 := ""
	isDirect := false

	flag.StringVar(&md5, "md5", "", "文件md5值")
	flag.StringVar(&sha1, "sha1", "", "文件sha1值")
	flag.BoolVar(&isDirect, "direct", false, "是否直达QAX")

	flag.Parse()

	if md5 == "" && sha1 == "" {
		fmt.Println("error: param error.")
		fmt.Println("Use example:" +
			" file_level_check.exe -md5 md5值 [-sha1 sha1值] [-direct true|false]")
		return
	}
	conn, err := grpc.Dial("192.168.111.195:38889", grpc.WithTransportCredentials(insecure.NewCredentials()))

	if err != nil {
		fmt.Println("error: did not connect.", err)
		return
	}
	defer conn.Close()

	client := rmmd5.NewGetMd5ServiceClient(conn)

	md5Info, err := client.GetMd5Info(context.Background(), &rmmd5.GetMd5InfoRequest{
		RequestCon: &rmmd5.RequestCon{
			Md5:  md5,
			Sha1: sha1,
		},
		IsDirect: isDirect,
		//Md5:  "63ec62319605b43d68eb25b9f84153c8", Sha1: "db417c63e3696ef327467544e7abfbbc11e1d30e",
	})
	if err != nil {
		fmt.Println("error:", err)
		return
	}
	fmt.Println("result: ", md5Info)
}
*/
