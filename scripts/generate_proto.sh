#!/bin/bash

# Protobuf 代码生成脚本
# 确保所有 .pb.go 文件使用相同版本的工具生成

set -e

# 检查必要的工具
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "错误: $1 未安装"
        exit 1
    fi
}

echo "检查必要工具..."
check_tool protoc
check_tool protoc-gen-go
check_tool protoc-gen-go-grpc

# 显示版本信息
echo "工具版本信息:"
echo "protoc: $(protoc --version)"
echo "protoc-gen-go: $(protoc-gen-go --version)"
echo "protoc-gen-go-grpc: $(protoc-gen-go-grpc --version)"

# 设置输出目录
OUTPUT_DIR="rmgrpc"
PROTO_DIR="configs/proto"

# 创建输出目录
mkdir -p $OUTPUT_DIR

echo "开始生成 protobuf 代码..."

# 生成 rmmd5 相关代码
if [ -f "$PROTO_DIR/rmmd5.proto" ]; then
    echo "生成 rmmd5.proto..."
    protoc --go_out=$OUTPUT_DIR --go-grpc_out=$OUTPUT_DIR $PROTO_DIR/rmmd5.proto
else
    echo "警告: $PROTO_DIR/rmmd5.proto 不存在"
fi

# 生成 cloudquery 相关代码
if [ -f "rmgrpc/cloudquery/proto/cloudquery.proto" ]; then
    echo "生成 cloudquery.proto..."
    protoc --go_out=rmgrpc/cloudquery --go-grpc_out=rmgrpc/cloudquery rmgrpc/cloudquery/proto/cloudquery.proto
fi

echo "代码生成完成!"

# 验证生成的文件
echo "验证生成的文件..."
for file in $(find $OUTPUT_DIR -name "*.pb.go"); do
    if [ -f "$file" ]; then
        echo "✓ $file"
        # 检查版本信息
        version_info=$(head -10 "$file" | grep -E "(protoc-gen-go|protoc)" || true)
        if [ ! -z "$version_info" ]; then
            echo "  版本: $version_info"
        fi
    fi
done

echo "完成!"
