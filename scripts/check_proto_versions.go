package main

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

// ProtobufFileInfo 存储 protobuf 文件的版本信息
type ProtobufFileInfo struct {
	FilePath       string
	ProtocVersion  string
	ProtocGenGo    string
	ProtocGenGrpc  string
}

func main() {
	fmt.Println("检查 protobuf 文件版本一致性...")

	// 查找所有 .pb.go 文件
	pbFiles, err := findProtobufFiles(".")
	if err != nil {
		fmt.Printf("错误: %v\n", err)
		os.Exit(1)
	}

	if len(pbFiles) == 0 {
		fmt.Println("未找到 .pb.go 文件")
		return
	}

	// 分析每个文件的版本信息
	var fileInfos []ProtobufFileInfo
	for _, file := range pbFiles {
		info, err := analyzeProtobufFile(file)
		if err != nil {
			fmt.Printf("警告: 分析文件 %s 失败: %v\n", file, err)
			continue
		}
		fileInfos = append(fileInfos, info)
	}

	// 显示结果
	fmt.Printf("\n找到 %d 个 protobuf 文件:\n", len(fileInfos))
	fmt.Println(strings.Repeat("-", 80))

	for _, info := range fileInfos {
		fmt.Printf("文件: %s\n", info.FilePath)
		if info.ProtocVersion != "" {
			fmt.Printf("  protoc: %s\n", info.ProtocVersion)
		}
		if info.ProtocGenGo != "" {
			fmt.Printf("  protoc-gen-go: %s\n", info.ProtocGenGo)
		}
		if info.ProtocGenGrpc != "" {
			fmt.Printf("  protoc-gen-go-grpc: %s\n", info.ProtocGenGrpc)
		}
		fmt.Println()
	}

	// 检查版本一致性
	checkVersionConsistency(fileInfos)
}

// findProtobufFiles 查找所有 .pb.go 文件
func findProtobufFiles(root string) ([]string, error) {
	var pbFiles []string

	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if strings.HasSuffix(path, ".pb.go") {
			pbFiles = append(pbFiles, path)
		}

		return nil
	})

	return pbFiles, err
}

// analyzeProtobufFile 分析单个 protobuf 文件的版本信息
func analyzeProtobufFile(filePath string) (ProtobufFileInfo, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return ProtobufFileInfo{}, err
	}
	defer file.Close()

	info := ProtobufFileInfo{FilePath: filePath}
	scanner := bufio.NewScanner(file)
	lineCount := 0

	// 只读取前20行，版本信息通常在文件头部
	for scanner.Scan() && lineCount < 20 {
		line := scanner.Text()
		lineCount++

		// 匹配版本信息
		if strings.Contains(line, "protoc-gen-go v") {
			re := regexp.MustCompile(`protoc-gen-go v([0-9.]+)`)
			if matches := re.FindStringSubmatch(line); len(matches) > 1 {
				info.ProtocGenGo = matches[1]
			}
		}

		if strings.Contains(line, "protoc-gen-go-grpc v") {
			re := regexp.MustCompile(`protoc-gen-go-grpc v([0-9.]+)`)
			if matches := re.FindStringSubmatch(line); len(matches) > 1 {
				info.ProtocGenGrpc = matches[1]
			}
		}

		if strings.Contains(line, "protoc        v") {
			re := regexp.MustCompile(`protoc\s+v([0-9.]+)`)
			if matches := re.FindStringSubmatch(line); len(matches) > 1 {
				info.ProtocVersion = matches[1]
			}
		}
	}

	return info, scanner.Err()
}

// checkVersionConsistency 检查版本一致性
func checkVersionConsistency(fileInfos []ProtobufFileInfo) {
	fmt.Println(strings.Repeat("=", 80))
	fmt.Println("版本一致性检查:")

	// 收集所有版本
	protocVersions := make(map[string][]string)
	protocGenGoVersions := make(map[string][]string)
	protocGenGrpcVersions := make(map[string][]string)

	for _, info := range fileInfos {
		if info.ProtocVersion != "" {
			protocVersions[info.ProtocVersion] = append(protocVersions[info.ProtocVersion], info.FilePath)
		}
		if info.ProtocGenGo != "" {
			protocGenGoVersions[info.ProtocGenGo] = append(protocGenGoVersions[info.ProtocGenGo], info.FilePath)
		}
		if info.ProtocGenGrpc != "" {
			protocGenGrpcVersions[info.ProtocGenGrpc] = append(protocGenGrpcVersions[info.ProtocGenGrpc], info.FilePath)
		}
	}

	// 检查 protoc 版本
	if len(protocVersions) > 1 {
		fmt.Println("❌ protoc 版本不一致:")
		for version, files := range protocVersions {
			fmt.Printf("  v%s: %v\n", version, files)
		}
	} else {
		fmt.Println("✅ protoc 版本一致")
	}

	// 检查 protoc-gen-go 版本
	if len(protocGenGoVersions) > 1 {
		fmt.Println("❌ protoc-gen-go 版本不一致:")
		for version, files := range protocGenGoVersions {
			fmt.Printf("  v%s: %v\n", version, files)
		}
	} else {
		fmt.Println("✅ protoc-gen-go 版本一致")
	}

	// 检查 protoc-gen-go-grpc 版本
	if len(protocGenGrpcVersions) > 1 {
		fmt.Println("❌ protoc-gen-go-grpc 版本不一致:")
		for version, files := range protocGenGrpcVersions {
			fmt.Printf("  v%s: %v\n", version, files)
		}
	} else {
		fmt.Println("✅ protoc-gen-go-grpc 版本一致")
	}

	// 总结
	inconsistentCount := 0
	if len(protocVersions) > 1 {
		inconsistentCount++
	}
	if len(protocGenGoVersions) > 1 {
		inconsistentCount++
	}
	if len(protocGenGrpcVersions) > 1 {
		inconsistentCount++
	}

	fmt.Println(strings.Repeat("-", 80))
	if inconsistentCount > 0 {
		fmt.Printf("⚠️  发现 %d 个版本不一致问题\n", inconsistentCount)
		fmt.Println("建议:")
		fmt.Println("1. 使用统一版本的 protoc 工具重新生成所有 .pb.go 文件")
		fmt.Println("2. 在 CI/CD 中添加版本检查")
		fmt.Println("3. 使用 scripts/generate_proto.sh 脚本统一生成")
	} else {
		fmt.Println("🎉 所有 protobuf 文件版本一致!")
	}
}
