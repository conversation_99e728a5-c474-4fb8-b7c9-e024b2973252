package main

import (
	"encoding/json"
	"fmt"
	"log"
)

// 模拟服务端结构 (uint64)
type ServerResponse struct {
	Code uint64 `json:"code"`
	Msg  string `json:"msg"`
}

// 模拟客户端结构 (int32)
type ClientResponse struct {
	Code int32  `json:"code"`
	Msg  string `json:"msg"`
}

// 模拟客户端结构 (int)
type ClientResponseInt struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

// 模拟客户端结构 (uint32)
type ClientResponseUint32 struct {
	Code uint32 `json:"code"`
	Msg  string `json:"msg"`
}

func main() {
	// 服务端发送数据
	serverResp := ServerResponse{
		Code: 99999,
		Msg:  "not found",
	}

	// 序列化为 JSON
	jsonData, err := json.Marshal(serverResp)
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("JSON 数据: %s\n", jsonData)

	// 测试不同客户端类型的反序列化
	fmt.Println("\n=== 反序列化测试 ===")

	// 1. 客户端使用 int32
	var clientResp1 ClientResponse
	err = json.Unmarshal(jsonData, &clientResp1)
	if err != nil {
		fmt.Printf("int32 反序列化失败: %v\n", err)
	} else {
		fmt.Printf("int32 反序列化成功: Code=%d, 类型=%T\n", clientResp1.Code, clientResp1.Code)
		fmt.Printf("int32 比较 99999: %t\n", clientResp1.Code == 99999)
	}

	// 2. 客户端使用 int
	var clientResp2 ClientResponseInt
	err = json.Unmarshal(jsonData, &clientResp2)
	if err != nil {
		fmt.Printf("int 反序列化失败: %v\n", err)
	} else {
		fmt.Printf("int 反序列化成功: Code=%d, 类型=%T\n", clientResp2.Code, clientResp2.Code)
		fmt.Printf("int 比较 99999: %t\n", clientResp2.Code == 99999)
	}

	// 3. 客户端使用 uint32
	var clientResp3 ClientResponseUint32
	err = json.Unmarshal(jsonData, &clientResp3)
	if err != nil {
		fmt.Printf("uint32 反序列化失败: %v\n", err)
	} else {
		fmt.Printf("uint32 反序列化成功: Code=%d, 类型=%T\n", clientResp3.Code, clientResp3.Code)
		fmt.Printf("uint32 比较 99999: %t\n", clientResp3.Code == 99999)
	}

	// 4. 直接使用 interface{} 解析
	var genericResp map[string]interface{}
	err = json.Unmarshal(jsonData, &genericResp)
	if err != nil {
		fmt.Printf("interface{} 反序列化失败: %v\n", err)
	} else {
		code := genericResp["code"]
		fmt.Printf("interface{} 反序列化成功: Code=%v, 类型=%T\n", code, code)
		
		// 类型断言测试
		if codeFloat, ok := code.(float64); ok {
			fmt.Printf("interface{} 作为 float64: %f\n", codeFloat)
			fmt.Printf("interface{} float64 比较 99999: %t\n", codeFloat == 99999.0)
			fmt.Printf("interface{} int 转换比较 99999: %t\n", int(codeFloat) == 99999)
		}
	}

	fmt.Println("\n=== 大数值测试 ===")
	
	// 测试大数值的情况
	bigServerResp := ServerResponse{
		Code: 18446744073709551615, // uint64 最大值
		Msg:  "big number",
	}

	bigJsonData, _ := json.Marshal(bigServerResp)
	fmt.Printf("大数值 JSON: %s\n", bigJsonData)

	var bigGenericResp map[string]interface{}
	json.Unmarshal(bigJsonData, &bigGenericResp)
	bigCode := bigGenericResp["code"]
	fmt.Printf("大数值解析: %v, 类型=%T\n", bigCode, bigCode)
	
	if bigCodeFloat, ok := bigCode.(float64); ok {
		fmt.Printf("大数值作为 float64: %.0f\n", bigCodeFloat)
		fmt.Printf("原始值: %d\n", bigServerResp.Code)
		fmt.Printf("精度是否丢失: %t\n", uint64(bigCodeFloat) != bigServerResp.Code)
	}
}
