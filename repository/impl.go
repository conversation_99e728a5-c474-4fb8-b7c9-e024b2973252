/**
* Author: <PERSON>
* Email: wudong<PERSON>@rongma.com
* Date: 2024/5/6
* Time: 14:20
* Software: GoLand
 */

package repository

import (
	"context"

	"rongma.com/srms/models"
)

type ImplSRMS interface {
	OnQueryFileSecurity(ctx context.Context, md5 string, sha1 string, sha256 string) (*models.FileSecurityQueryResponse, error)
	OnUpload(ctx context.Context, r *models.FileUploadRequest) (*models.FileUploadResponse, error)
	OnUploadShard(ctx context.Context, r *models.FileShardUploadRequest) (*models.FileShardUploadResponse, error)
	OnQueryThreshold(ctx context.Context, partner string) (*models.Threshold, error)

	OnSaveMaliceFileInfo(ctx context.Context, maliceFile *models.FileSecurityQueryResponse, fileName string, orgName string) error
}
