package repository

import (
	"bytes"
	"context"
	baseErrors "errors"
	"fmt"
	"time"
	
	"github.com/go-basic/uuid"
	"github.com/minio/minio-go/v7"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"rm.git/client_api/rm_common_libs.git/v2/common/clients"
	"rm.git/client_api/rm_common_libs.git/v2/library/errors"
	
	srmsErr "rongma.com/srms/errors"
	
	"rongma.com/srms/config"
	"rongma.com/srms/models"
	"rongma.com/srms/modules/client"
	"rongma.com/srms/modules/logger"
)

var conf = config.Config()

type SRMSService struct {
	shards     *mongo.Collection
	files      *mongo.Collection
	maliceFile *mongo.Collection
	minio      *clients.Minio
}

func NewSRMSService() ImplSRMS {
	return &SRMSService{
		shards:     client.MongoSrms.Database.Collection("shards"),
		files:      client.MongoSrms.Database.Collection("files"),
		maliceFile: client.MongoSrms.Database.Collection("malice_file"),
		minio:      client.Minio,
	}
}

func (s *SRMSService) OnQueryFileSecurity(ctx context.Context, md5 string, sha1 string, sha256 string) (*models.FileSecurityQueryResponse, error) {
	var (
		filter bson.M
	)
	
	if md5 != "" {
		filter = bson.M{"md5": md5}
	} else if sha1 != "" {
		filter = bson.M{"sha1": sha1}
	} else if sha256 != "" {
		filter = bson.M{"sha256": sha256}
	}
	
	logger.WithContext(ctx).Infof("[query] query file filter: %v", filter)
	var sample models.SampleFile
	if err := s.files.FindOne(ctx, filter).Decode(&sample); err != nil {
		if baseErrors.Is(err, mongo.ErrNoDocuments) {
			response := &models.FileSecurityQueryResponse{
				MD5: md5,
			}
			response.NeedUpload = 1
			return response, nil
		}
		
		return nil, err
	}
	
	logger.WithContext(ctx).Debug("[query] query file result: %v", sample)
	
	response := &models.FileSecurityQueryResponse{
		MD5:    sample.MD5,
		SHA1:   sample.SHA1,
		SHA256: sample.SHA256,
		Level:  sample.Level,
		Extend: sample.Extend,
	}
	
	if sample.Status != int(models.Finished) { // TODO 处理
		response.NeedUpload = 1
	}
	
	return response, nil
}

func (s *SRMSService) OnUpload(ctx context.Context, r *models.FileUploadRequest) (*models.FileUploadResponse, error) {
	var (
		filter   bson.M
		fileGuid string
	)
	
	response := &models.FileUploadResponse{}
	
	if r.MD5 != "" {
		filter = bson.M{"md5": r.MD5}
	} else if r.SHA1 != "" {
		filter = bson.M{"sha1": r.SHA1}
	} else if r.SHA256 != "" {
		filter = bson.M{"sha256": r.SHA256}
	}
	
	logger.WithContext(ctx).Infof("[upload] query file filter: %v", filter)
	var sample models.SampleFile
	if err := s.files.FindOne(ctx, filter).Decode(&sample); err != nil {
		if !baseErrors.Is(err, mongo.ErrNoDocuments) {
			logger.WithContext(ctx).Errorf("[upload] query file filter: %v, error: %s", filter, err)
			return nil, err
		}
		
		// insert document
		fileGuid = uuid.New()
		insertFile := &models.SampleFile{
			GUID:       fileGuid,
			Name:       r.Name,
			MD5:        r.MD5,
			SHA1:       r.SHA1,
			SHA256:     r.SHA256,
			Size:       r.Size,
			Status:     0,
			CreateTime: time.Now().Unix(),
			ClientId:   r.ClientId,
			OrgName:    r.OrgName,
		}
		
		if _, err := s.files.InsertOne(ctx, insertFile); err != nil {
			logger.WithContext(ctx).Errorf("[upload] insert file document error: %v", err)
			return nil, errors.MongoInsertErr
		}
		
		logger.WithContext(ctx).Infof("[upload] insert document id: %v", fileGuid)
		
		if err := s.saveSplitShard(ctx, fileGuid, r.Size); err != nil {
			return nil, err
		}
		
	} else {
		if sample.Status == int(models.Finished) { // upload finished
			logger.WithContext(ctx).Infof("[upload] %v is finished", sample.GUID)
			return response, nil
		}
		// 查看分片文件是否存在，不存在则重新分片
		search := bson.M{"file_id": sample.GUID}
		if err := s.shards.FindOne(ctx, search).Err(); err != nil {
			if !baseErrors.Is(err, mongo.ErrNoDocuments) {
				logger.WithContext(ctx).Errorf("[upload] failed to call Find One Document err: %s", err)
				return nil, err
			}
			
			logger.WithContext(ctx).Infof("[upload] repair file shard record, md5: %s, file_id: %s", sample.MD5, sample.GUID)
			if err := s.saveSplitShard(ctx, sample.GUID, sample.Size); err != nil {
				return nil, err
			}
		}
		
	}
	
	// select shard
	searchFilter := bson.M{"file_id": sample.GUID, "status": 0}
	
	if fileGuid != "" {
		searchFilter["file_id"] = fileGuid
	}
	
	var shardInfo models.ShardInfo
	if err := s.shards.FindOne(context.Background(), searchFilter).Decode(&shardInfo); err != nil {
		if !baseErrors.Is(err, mongo.ErrNoDocuments) {
			logger.WithContext(ctx).Errorf("[upload] query shards filter: %v, error: %s", searchFilter, err)
			return nil, err
		}
		// 未上传的文件不存在，判断是否有分片文件，并修改当前文件的状态
		fileId := searchFilter["file_id"]
		if err := s.shards.FindOne(context.Background(), bson.M{"file_id": fileId}).Err(); err != nil {
			return nil, err
		}
		
		updateFilter := bson.M{"guid": fileId, "status": 0}
		update := bson.M{"$set": bson.M{"status": 1}}
		
		if updateErr := s.files.FindOneAndUpdate(context.Background(), updateFilter, update).Err(); updateErr != nil {
			if !baseErrors.Is(updateErr, mongo.ErrNoDocuments) {
				return nil, err
			}
		}
		
		logger.WithContext(ctx).Infof("[upload] repair files guid: %s ", fileId)
		return response, nil
		
	}
	
	response = &models.FileUploadResponse{
		Size:   shardInfo.Size,
		Offset: shardInfo.Offset,
		Token:  shardInfo.GUID,
	}
	
	return response, nil
}

// 保存分片记录
func (s *SRMSService) saveSplitShard(ctx context.Context, fileGuid string, fileSize int64) error {
	var (
		shardSize = conf.Service.ShardSize
		shardNum  = fileSize / shardSize
		remain    = fileSize % shardSize
		// insert shard info
		batchInsertArr []interface{}
		i              int64
		fileDir        = time.Now().Format("2006/01/02")
	)
	
	if remain > 0 {
		shardNum = shardNum + 1
	}
	
	for i = 0; i < shardNum; i++ {
		insertShard := bson.M{"guid": uuid.New(), "file_id": fileGuid, "size": shardSize, "block_size": shardSize,
			"offset": i * shardSize, "status": 0, "file_dir": fileDir, "create_time": time.Now().Unix()}
		
		if i == shardNum-1 && remain > 0 {
			insertShard["size"] = remain
		}
		batchInsertArr = append(batchInsertArr, insertShard)
	}
	
	if _, errs := s.shards.InsertMany(ctx, batchInsertArr); errs != nil {
		logger.WithContext(ctx).Errorf("[upload] insert shard document error: %v", errs)
		return errs
	}
	return nil
}

func (s *SRMSService) OnUploadShard(ctx context.Context, r *models.FileShardUploadRequest) (*models.FileShardUploadResponse, error) {
	
	response := &models.FileShardUploadResponse{}
	
	if len(r.Token) == 0 {
		logger.WithContext(ctx).Errorf("[shard] token is empty :%s", r.Token)
		return nil, errors.InvalidParameter
	}
	
	// get shard info by guid
	filter := bson.M{"guid": r.Token}
	
	var shardInfo models.ShardInfo
	if err := s.shards.FindOne(ctx, filter).Decode(&shardInfo); err != nil {
		if !baseErrors.Is(err, mongo.ErrNoDocuments) {
			logger.WithContext(ctx).Errorf("[shard] search shard info err:%s, filter:%s", err, filter)
			return nil, srmsErr.MongoQueryEmpty
		} else {
			logger.WithContext(ctx).Errorf("[shard] search shard info err:%s, filter:%s", err, filter)
			return nil, errors.InvalidParameter
		}
	}
	var remainShardInfo models.ShardInfo
	
	// 没有获取到锁
	// 检查文件的其他分片(未上传的)有没有设置锁
	// 返回没有设置锁的分片信息，否则就返回正常传成功的信息
	cacheKey := fmt.Sprintf("fileShard:%s", r.Token)
	isFileShardLock, fileShardLockErr := client.Redis.SetNX(ctx, cacheKey, r.Token, time.Second*60).Result()
	if fileShardLockErr != nil {
		logger.WithContext(ctx).Errorf("[shard] redis lock err, %s", fileShardLockErr)
		return nil, fileShardLockErr
	}
	if !isFileShardLock {
		logger.WithContext(ctx).Errorf("[shard] redis not fetch locked ")
		return response, nil
	}
	// 没有获取到锁处理结束
	
	if shardInfo.Status == 0 {
		sourceData := bytes.NewBufferString(r.Data).Bytes()
		// save shard data  这段代码没有多大的意义，判断桶存不存在，只是第一次判断就可以了
		// exists, err := client.Minio.BucketExists(context.Background(), conf.Service.ShardBucket)
		// if err != nil {
		// 	logger.WithContext(ctx).Errorf("[shard] bucket error, %v", err)
		// 	return nil, errors.MinioBucketExists
		// }
		//
		// if !exists {
		// 	logger.WithContext(ctx).Errorf("[shard] bucket not exists")
		// 	return nil, errors.MinioBucketExists
		// }
		
		objName := fmt.Sprintf("%s/%s", shardInfo.FileDir, r.Token)
		
		saveInfo, err := s.minio.Client.PutObject(ctx, client.MinioSrmsShardBucket, objName, bytes.NewReader(sourceData),
			int64(len(sourceData)), minio.PutObjectOptions{ContentType: "application/text"})
		if err != nil {
			logger.WithContext(ctx).Errorf("[shard] save shard file error, %v", err)
			return nil, errors.MinioUploadFileErr
		}
		
		// update shard status
		updateFilter := bson.M{"guid": r.Token, "status": 0}
		update := bson.M{"$set": bson.M{"status": 1, "bucket": saveInfo.Bucket}}
		var updateShardInfo models.ShardInfo
		updateErr := s.shards.FindOneAndUpdate(ctx, updateFilter, update).Decode(&updateShardInfo)
		if updateErr != nil {
			
			if !baseErrors.Is(updateErr, mongo.ErrNoDocuments) {
				return nil, updateErr
			}
		}
	}
	
	// find remain shard info
	searchFilter := bson.M{"file_id": shardInfo.FileId, "status": 0}
	// logger.WithContext(ctx).Infof("[upload shard] search remain shard info filter: %v", searchFilter)
	
	if searchErr := s.shards.FindOne(ctx, searchFilter).Decode(&remainShardInfo); searchErr != nil {
		if !baseErrors.Is(searchErr, mongo.ErrNoDocuments) {
			return nil, searchErr
		}
		// no remain shard info, update files info
		var updateFilesInfo models.SampleFile
		updateFilter := bson.M{"guid": shardInfo.FileId, "status": 0}
		update := bson.M{"$set": bson.M{"status": 1}}
		
		if updateErr := s.files.FindOneAndUpdate(ctx, updateFilter, update).Decode(&updateFilesInfo); updateErr != nil {
			// not mongo.ErrNoDocuments
			if !baseErrors.Is(updateErr, mongo.ErrNoDocuments) {
				return nil, updateErr
			}
		}
		
		logger.WithContext(ctx).Infof("[shard] no shard info, update files info status finished")
		
		return response, nil
		
	}
	
	if len(remainShardInfo.GUID) == 0 {
		return response, nil
	}
	
	response = &models.FileShardUploadResponse{
		Size:   remainShardInfo.Size,
		Offset: remainShardInfo.Offset,
		Token:  remainShardInfo.GUID,
	}
	
	return response, nil
}

func (s *SRMSService) OnQueryThreshold(ctx context.Context, partner string) (*models.Threshold, error) {
	thresholdInfo := &models.Threshold{}
	
	return thresholdInfo, nil
	
}

func (s *SRMSService) OnSaveMaliceFileInfo(ctx context.Context, maliceFile *models.FileSecurityQueryResponse, fileName string, orgName string) error {
	document := &models.MaliceFile{
		Name:       fileName,
		MD5:        maliceFile.MD5,
		SHA1:       maliceFile.SHA1,
		SHA256:     maliceFile.SHA256,
		Level:      maliceFile.Level,
		RiskLevel:  "high",
		CreateTime: time.Now().Unix(),
		UpdateTime: time.Now().Unix(),
		SaveTime:   time.Now(),
		OrgName:    orgName,
	}
	
	_, err := s.maliceFile.InsertOne(ctx, document)
	if err != nil {
		return errors.MongoInsertErr
	}
	return nil
}

func (s *SRMSService) OnFileQueryCount(ctx context.Context, fileName string) {
	now := time.Now().Format("2006-01-02")
	key := fmt.Sprintf("query_file_count_%s", now)
	pipe := client.Redis
	total, _ := pipe.ZCard(ctx, key).Result()
	if total == 0 {
		pipe.ZAdd(ctx, key, redis.Z{Score: 1, Member: fileName})
		pipe.Expire(ctx, key, time.Hour*24*7)
	} else if total > 0 {
		if _, err := pipe.ZScore(ctx, key, fileName).Result(); err == redis.Nil {
			pipe.ZAdd(ctx, key, redis.Z{Score: 1, Member: fileName})
		} else {
			pipe.ZIncrBy(ctx, key, 1, fileName)
		}
	}
}
