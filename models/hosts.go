package models

type HostInfo struct { //nolint:maligned
	Id                  string         `json:"id"`
	ClientID            string         `json:"client_id"`
	TokenId             string         `json:"token_id"`
	HostName            string         `json:"hostname"`
	OSVersion           string         `json:"os_version"`
	OsType              int            `json:"os_type"`
	Platform            int32          `json:"platform"`
	WinVersion          string         `json:"win_version"`
	Importance          int            `json:"importance"`
	IPAddress           string         `json:"ip_address"`
	MacAddress          string         `json:"mac_address"`
	CPUUsage            string         `json:"cpu_usage"`
	DiskUsage           string         `json:"disk_usage"`
	Diskspace           string         `json:"diskspace"`
	MemoryUsage         string         `json:"memory_usage"`
	KernelStatus        string         `json:"kernel_status"`
	KernelVer           string         `json:"kernel_ver"`
	ClientVersion       string         `json:"client_version"`
	LastActive          int64          `json:"last_active"`
	LastActiveTime      string         `json:"last_active_time"`
	LatestDetectionTime string         `json:"latest_detection_time"`
	Status              string         `json:"status"`
	Tags                []string       `json:"tags"`
	Remarks             string         `json:"remarks"`
	Enabled             bool           `json:"enabled"`
	GroupId             string         `json:"group_id"`
	OrgName             string         `json:"org_name"`
	ClientIp            string         `json:"client_ip"`
	InternetIp          string         `json:"internet_ip"`
	ServiceStatus       string         `json:"service_status"`
	RmService           string         `json:"rm_service"`
	RmLive              string         `json:"rm_live"`
	RmTray              string         `json:"rm_tray"`
	Licence             string         `json:"licence"`
	RiskLevel           string         `json:"risk_level"`
	Isolated            int32          `json:"isolated"`
	CreateTime          int64          `json:"create_time"`
	InstallTime         int64          `json:"install_time"`
	Ipconfig            []IpconfigInfo `json:"ipconfig"`
	Username            string         `json:"username"`
	LastLogonTime       int64          `json:"lastlogontime"`
	Processorname       string         `json:"processorname"`
	Diskidentifier      string         `json:"diskidentifier"`
	Domain              string         `json:"domain"`
	Workgroup           string         `json:"workgroup"`
	RmConnectip         string         `json:"rmconnectip"`
	OrgConnectip        string         `json:"orgconnectip"`
	OsBuild             int            `json:"os_build"`
}

type IpconfigInfo struct {
	Desc    string   `json:"desc"`
	IpList  []Ipinfo `json:"iplist"`
	Gateway []string `json:"gateway"`
	Dns     []string `json:"dns"`
	Mac     string   `json:"mac"`
}

type Ipinfo struct {
	Ip   string `json:"ip"`
	Mask string `json:"mask"`
}
