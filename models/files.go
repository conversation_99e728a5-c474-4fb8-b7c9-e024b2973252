package models

import "time"

type SampleStatus int

var (
	Finished SampleStatus = 1
	NotFound SampleStatus = 0
)

type SampleFile struct {
	GUID       string                 `bson:"guid"`
	Name       string                 `bson:"name"`
	MD5        string                 `bson:"md5"`
	SHA1       string                 `bson:"sha1"`
	SHA256     string                 `bson:"sha256"`
	Size       int64                  `bson:"size"`
	Level      int                    `bson:"level"`
	Status     int                    `bson:"status"`
	Shards     []ShardInfo            `bson:"shards"`
	CreateTime int64                  `bson:"create_time"`
	Extend     map[string]interface{} `bson:"extend"`
	ClientId   string                 `bson:"client_id"`
	OrgName    string                 `bson:"org_name"`
}

type ShardInfo struct {
	GUID      string `bson:"guid"`
	BlockSize int64  `bson:"block_size"`
	FileId    string `bson:"file_id"`
	Size      int64  `bson:"size"`
	Offset    int64  `bson:"offset"`
	Bucket    string `bson:"bucket"`
	Status    int    `bson:"status"`
	FileDir   string `bson:"file_dir"`
	// CreateTime   int64  `bson:"create_time"`
}

type DumpFiles struct {
	GUID       string `bson:"guid"`
	FileId     string `bson:"file_id"`
	Size       int64  `bson:"size"`
	Bucket     string `bson:"bucket"`
	CreateTime int64  `bson:"create_time"`
	Status     int    `bson:"status"`
}

type MaliceFile struct {
	Name       string    `bson:"name"`
	MD5        string    `bson:"md5"`
	SHA1       string    `bson:"sha1"`
	SHA256     string    `bson:"sha256"`
	Level      int       `bson:"level"`
	RiskLevel  string    `bson:"risk_level"`
	CreateTime int64     `bson:"create_time"`
	UpdateTime int64     `bson:"update_time"`
	SaveTime   time.Time `bson:"save_time"`
	OrgName    string    `bson:"org_name"`
}

type QuarantineFile struct {
	Id               interface{} `bson:"_id,omitempty"`
	CurrentIndex     int64       `bson:"current_index"`
	XorKey           int64       `bson:"xor_key"`
	OriginalMd5      string      `bson:"original_md5"`
	OriginalSha1     string      `bson:"original_sha1"`
	OriginalNtName   string      `bson:"original_nt_name"`
	QuarantineMd5    string      `bson:"quarantine_md5"`
	QuarantineSha1   string      `bson:"quarantine_sha1"`
	QuarantineNtName string      `bson:"quarantine_nt_name"`
	FileSize         int64       `bson:"file_size"`
	BSelect          bool        `bson:"b_select"`
	CreateTime       int64       `bson:"create_time"`
	UpdateTime       int64       `bson:"update_time"`
	UploadTime       time.Time   `bson:"upload_time"`
	ClientId         string      `bson:"client_id"`
	User             string      `bson:"user"`
	Status           int         `bson:"status"`
	OrgName          string      `bson:"org_name"`
}

type TrustFile struct {
	RememberAllow      bool      `json:"remember_allow" bson:"remember_allow"`
	RememberBlock      bool      `json:"remember_block" bson:"remember_block"`
	MFileCreationTime  int64     `json:"m_file_creation_time" bson:"m_file_creation_time"`
	MFileAccessTime    int64     `json:"m_file_access_time" bson:"m_file_access_time"`
	MFileLastWriteTime int64     `json:"m_file_last_write_time" bson:"m_file_last_write_time"`
	Fltr               []Fltr    `json:"fltr" bson:"fltr"`
	MFile              string    `json:"m_file" bson:"m_file"`
	MMd5A              string    `json:"m_md5_a" bson:"m_md5_a"`
	MSha1A             string    `json:"m_sha1_a" bson:"m_sha1_a"`
	MFileName          string    `json:"m_file_name" bson:"m_file_name"`
	ClientId           string    `json:"client_id" bson:"client_id"`
	User               string    `json:"user" bson:"user"`
	Status             int       `json:"status" bson:"status"`
	CreateTime         int64     `json:"create_time" bson:"create_time"`
	UpdateTime         int64     `json:"update_time" bson:"update_time"`
	UploadTime         time.Time `json:"upload_time" bson:"upload_time"`
	OrgName            string    `json:"org_name" bson:"org_name"`
}

type Fltr struct {
	MFltrId          int64  `json:"m_fltr_id" bson:"m_fltr_id"`
	MFltrDescription string `json:"m_fltr_description" bson:"m_fltr_description"`
}

type FileSecurityCache struct {
	Level  int `json:"level"`
	Extend map[string]interface{}
}

// FileCacheValue Redis 存储的数据值
type FileCacheValue struct {
	Level  int                    `json:"level"`
	Extend map[string]interface{} `json:"extend"`
}

type IsolateFileLevel struct {
	Hash       string  `json:"hash" bson:"hash"`
	CreateTime int64   `json:"create_time" bson:"create_time"`
	UpdateTime int64   `json:"update_time" bson:"update_time"`
	OperatorId string  `json:"operator_id" bson:"operator_id"`
	FileName   string  `json:"file_name" bson:"file_name"`
	OrgName    string  `json:"org_name" bson:"org_name"`
	Level      int     `json:"level" bson:"level"`
	HostType   string  `json:"host_type" bson:"host_type"`
	GroupIds   []int64 `json:"group_ids" bson:"group_ids"`
}

type IsolateFile struct {
	GUID               string `json:"guid" bson:"guid"`
	OrgName            string `json:"org_name" bson:"org_name"`
	ClientId           string `json:"client_id" bson:"client_id"`
	Md5                string `json:"md5" bson:"md5"`
	Category           int32  `json:"category" bson:"category"` // 类型
	Sha1               string `json:"sha1" bson:"sha1"`
	FileName           string `json:"file_name" bson:"file_name"`
	HostName           string `json:"hostname" bson:"hostname"`
	Username           string `json:"username" bson:"username"`
	RecoverStatus      uint8  `json:"recover_status" bson:"recover_status"`
	TaskId             string `json:"task_id" bson:"task_id"`
	OperateTime        int64  `json:"operate_time" bson:"operate_time"`
	LastQuarantineTime int64  `json:"last_quarantine_time" bson:"last_quarantine_time"`
	CreateTime         int64  `json:"create_time" bson:"create_time"`
	FilePath           string `json:"file_path" bson:"file_path"`
}

type IsolateFileExt struct {
	IsolateFileId string `json:"isolate_file_id" bson:"isolate_file_id"`
	RootUuid      string `json:"root_uuid" bson:"root_uuid"`
	CreatedTime   int64  `json:"created_time" bson:"created_time"`
}
