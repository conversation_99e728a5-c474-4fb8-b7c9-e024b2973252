package models

import "encoding/json"

type FileSecurityBatchQueryRequest struct {
	OrgName   string                `mapstructure:"org_name" json:"org_name"`
	ClientID  string                `mapstructure:"client_id" json:"client_id"`
	QueryList []BatchQueryBaseParam `mapstructure:"query_list" json:"query_list"`
}
type BatchQueryBaseParam struct {
	Md5    string `mapstructure:"md5,omitempty" json:"md5,omitempty"`
	Sha1   string `mapstructure:"sha1,omitempty" json:"sha1,omitempty"`
	Level  int    `mapstructure:"level,omitempty" json:"level"`
	Extend string `mapstructure:"extend,omitempty" json:"extend"`
}

type FileSecurityQueryRequest struct {
	Query                  string `mapstructure:"query" json:"query"`
	ITick                  string `mapstructure:"i_tick,omitempty" json:"i_tick,omitempty"`
	MD5                    string `mapstructure:"md5,omitempty" json:"md5,omitempty" validate:"required"`
	SMD5                   string `mapstructure:"s_md5,omitempty" json:"s_md5,omitempty"`
	SHA1                   string `mapstructure:"sha1,omitempty" json:"sha1,omitempty" validate:"required"`
	SSHA1                  string `mapstructure:"s_sha1,omitempty" json:"s_sha1,omitempty"`
	SHA256                 string `mapstructure:"sha256,omitempty" json:"sha256,omitempty"`
	SSHA256                string `mapstructure:"s_sha256,omitempty" json:"s_sha256,omitempty"`
	CommandLineFile        string `mapstructure:"commandlinefile,omitempty" json:"commandLineFile,omitempty"`
	SCommandLineFile       string `mapstructure:"s_commandlinefile,omitempty" json:"s_CommandLineFile,omitempty"`
	CommandLineFileMd5     string `mapstructure:"commandlinefilemd5,omitempty" json:"commandLineFileMd5,omitempty"`
	SCommandLineFileMd5    string `mapstructure:"s_commandlinefilemd5,omitempty" json:"s_CommandLineFileMd5,omitempty"`
	CommandLineFileSha1    string `mapstructure:"commandlinefilesha1,omitempty" json:"commandLineFileSha1,omitempty"`
	SCommandLineFileSha1   string `mapstructure:"s_commandlinefilesha1,omitempty" json:"s_CommandLineFileSha1,omitempty"`
	CommandLineFileSha256  string `mapstructure:"commandlinefilesha256,omitempty" json:"commandLineFileSha256,omitempty"`
	SCommandLineFileSha256 string `mapstructure:"s_commandlinefilesha256,omitempty" json:"s_CommandLineFileSha256,omitempty"`
}

type FileSecurityQueryResponse struct {
	// Name       string                 `json:"name,omitempty"`
	MD5                       string                 `json:"md5,omitempty"`
	SHA1                      string                 `json:"sha1,omitempty"`
	SHA256                    string                 `json:"sha256,omitempty"`
	Level                     int                    `json:"level"`
	LevelSource               int                    `json:"level_source"`
	Extend                    map[string]interface{} `json:"extend"`
	NeedUpload                int                    `json:"need_upload"`
	CommandLineFileMD5        string                 `json:"commandlinefile_md5,omitempty"`
	CommandLineFileSHA1       string                 `json:"commandlinefile_sha1,omitempty"`
	CommandLineFileSHA256     string                 `json:"commandlinefile_sha256,omitempty"`
	CommandLineFileLevel      int                    `json:"commandlinefile_level"`
	CommandLineFileExtend     map[string]interface{} `json:"commandlinefile_extend,omitempty"`
	CommandLineFileNeedUpload int                    `json:"commandlinefile_need_upload"`
}

func (f FileSecurityQueryResponse) MarshalBinary() ([]byte, error) {
	return json.Marshal(f)
}
func (f FileSecurityQueryResponse) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &f)
}

type FileUploadRequest struct {
	ClientId string `mapstructure:"client_id" json:"client_id"`
	Name     string `json:"name"`
	Size     int64  `json:"size" validate:"required"`
	MD5      string `json:"md5,omitempty" validate:"required"`
	SHA1     string `json:"sha1,omitempty" validate:"required"`
	SHA256   string `json:"sha256,omitempty" `
	OrgName  string `json:"org_name"`
}

type FileUploadResponse struct {
	Size   int64  `json:"size,omitempty"`
	Offset int64  `json:"offset"`
	Token  string `json:"token,omitempty"`
}

type FileShardUploadRequest struct {
	Data  string `json:"data"`
	Token string `json:"token"`
}

type FileShardUploadResponse struct {
	Size   int64  `json:"size,omitempty"`
	Offset int64  `json:"offset"`
	Token  string `json:"token,omitempty"`
}

type UploadToken struct {
	Token   string `json:"token"`
	Shard   string `json:"shard"`
	Size    string `json:"size"`
	Expires int64  `json:"expires"`
}

type Threshold struct {
	PartnerID     string `json:"partner_id" bson:"partner_id"`
	Limit         int64  `json:"limit" bson:"limit"`
	ExpiresMinute int64  `json:"expire_minute" bson:"expire_minute"`
}

type DumpUploadRequest struct {
	Data     string `json:"data"`
	FileName string `json:"file_name"`
	OrgName  string `json:"org_name"`
}

type DumpUploadResponse struct {
	FileID   string `json:"file_id"`
	FileName string `json:"file_name,omitempty"`
	Size     int64  `json:"size,omitempty"`
}

type DumpConfigRequest struct {
	ProductVer string `mapstructure:"product_version" json:"product_version"`
	ExtData    string `mapstructure:"ext" json:"ext"`
}

type DumpConfigResponse struct {
	IntervalTime     int64  `bson:"interval_time" json:"interval_time"`
	DumpTypeR0       int64  `bson:"dump_type_r0" json:"dump_type_r0"`
	DumpTypeR3       int64  `bson:"dump_type_r3" json:"dump_type_r3"`
	DumpSize         int64  `bson:"dump_size" json:"dump_size"`
	ThirdProcessList string `bson:"third_process_list" json:"third_process_list"`
	ExtData          string `bson:"ext" json:"ext"`
	Status           int64  `bson:"status" json:"status"`
}

type UploadConfigRequest struct {
	ProductVer string `mapstructure:"product_version" json:"product_version"`
	ExtData    string `mapstructure:"ext" json:"ext"`
}

type UploadConfigResponse struct {
	Quantity        int `json:"quantity"`
	IntervalTime    int `json:"interval_time"`
	APIIntervalTime int `json:"api_interval_time"`
}

type UploadConfigRes struct {
	IntervalTime     int64  `bson:"interval_time" json:"interval_time"`
	Quantity         int64  `bson:"quantity" json:"quantity"`
	UploadTypeR0     int64  `bson:"upload_type_r0" json:"dump_type_r0"`
	UploadTypeR3     int64  `bson:"upload_type_r3" json:"dump_type_r3"`
	UploadSize       int64  `bson:"upload_size" json:"dump_size"`
	ThirdProcessList string `bson:"third_process_list" json:"third_process_list"`
	ExtData          string `bson:"ext" json:"ext"`
	Status           int64  `bson:"status" json:"status"`
}

type QuarantineFileRequest struct {
	ClientId         string `json:"client_id"`
	CurrentIndex     int64  `json:"current_index"`
	XorKey           int64  `json:"xor_key"`
	OriginalMd5      string `json:"original_md5"`
	OriginalSha1     string `json:"original_sha1"`
	OriginalNtName   string `json:"original_nt_name"`
	QuarantineMd5    string `json:"quarantine_md5"`
	QuarantineSha1   string `json:"quarantine_sha1"`
	QuarantineNtName string `json:"quarantine_nt_name"`
	User             string `json:"user"`
	FileSize         int64  `json:"file_size"`
	BSelect          bool   `json:"b_select"`
}

type TrustFileRequest struct {
	RememberAllow      bool   `json:"remember_allow"`
	RememberBlock      bool   `json:"remember_block"`
	MFileCreationTime  int64  `json:"m_file_creation_time"`
	MFileAccessTime    int64  `json:"m_file_access_time"`
	MFileLastWriteTime int64  `json:"m_file_last_write_time"`
	Fltr               []Fltr `json:"fltr"`
	MFile              string `json:"m_file"`
	MMd5A              string `json:"m_md5_a"`
	MSha1A             string `json:"m_sha1_a"`
	MFileName          string `json:"m_file_name"`
	User               string `json:"user"`
}

type FileQueryRankRequest struct {
	Date string `json:"date"`
	Rank int64  `json:"rank"`
}

type FileQueryRankResponse struct {
	FileName interface{} `json:"file_name"`
	Score    float64     `json:"score"`
}

type CloudQueryResponse struct {
	Out map[string]interface{} `json:"out"`
}

type AcquireChannel struct {
	SecretKey   string `json:"secret_key"`
	CipherSuite string `json:"cipher_suite"`
}
type RequestAuth struct {
	ClientID   string `json:"client_id"`
	ClientIP   string `json:"client_ip"`
	Channel    string `json:"channel"`
	Token      string `json:"token"`
	Content    []byte `json:"content"`
	Printable  bool   `json:"printable"`
	Compressed bool   `json:"compressed"`
	Session    string `json:"session"`
	OrgName    string `json:"org_name"`
}

type RMRequestData struct {
	Data    map[string]string
	Token   *RMToken
	Session *RMSession
}

// RMToken 定义基础接收数据格式
type RMToken struct {
	ClientId   string
	TokenKeyId string
	OrgName    string
}

type RMSession struct {
}

type QueryReq struct {
	Md5  string `json:"md5"   validate:"required"`
	Sha1 string `json:"sha1" validate:"required"`
}

type QueryResponse struct {
	Md5     string                 `json:"md5"`
	Level   int                    `json:"level"`
	Sha1    string                 `json:"sha1"`
	Extend  map[string]interface{} `json:"extend"`
	IsFound bool                   `json:"is_found"`
}

type IsolateFileReq struct {
	Md5      string `json:"md5" validate:"required"`
	ShA1     string `json:"sha1" validate:"required"`
	FilePath string `json:"file_path" validate:"required"`
	FileName string `json:"file_name" validate:"required"`
	RootUUID string `json:"root_uuid" `
	Category int32  `json:"category" default:"1"` //这个参数 默认是 “1”，其中 1 代表自动，0代表 人工，兼容之前的数据为单独处理
}
