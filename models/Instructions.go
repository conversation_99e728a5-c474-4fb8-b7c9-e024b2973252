package models

type Instructions struct {
	ID              string `json:"id" bson:"_id,omitempty"`
	InstructionName string `json:"instruction_name" bson:"instruction_name"`
	Contents        string `json:"contents" bson:"contents"`
	SearchContent   string `json:"search_content" bson:"search_content"`
	Status          int    `json:"status" bson:"status"`
	OrgName         string `json:"org_name" bson:"org_name"`
	Key             string `json:"key" bson:"key"`
	ClientID        string `json:"client_id" bson:"client_id"`
	HostName        string `json:"host_name" bson:"host_name"`
	ResponseContent string `json:"response_content" bson:"response_content"`
	ResponseTime    int64  `json:"response_time" bson:"response_time"`
	ResponseMsg     string `json:"response_msg" bson:"response_msg"`
	CreateTime      int64  `json:"create_time" bson:"create_time"`
	UpdateTime      int64  `json:"update_time" bson:"update_time"`
	ActivityTime    int64  `json:"activity_time" bson:"activity_time"`
	OperationUser   string `json:"operation_user" bson:"operation_user"`
	OperationUID    string `json:"operation_uid" bson:"operation_uid"`
	ErrorCode       int    `json:"error_code" bson:"error_code"`
}
