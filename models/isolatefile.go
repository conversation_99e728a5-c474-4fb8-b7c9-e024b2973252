/**
* Author: Jason
* Email: wudong<PERSON>@rongma.com
* Date: 2024/10/17
* Time: 16:56
* Software: GoLand
 */

package models

type KafkaMsg struct {
	ClientID    string `json:"client_id"`
	FileStatus  int64  `json:"file_status"`
	OrgName     string `json:"org_name"`
	Source      string `json:"source"`
	VirusType   int64  `json:"virus_type"`
	CheckTime   int64  `json:"check_time"`
	DetectionID string `json:"detection_id"`
	FileMd5     string `json:"file_md5"`
	FileName    string `json:"file_name"`
	FilePath    string `json:"file_path"`
	FileSha1    string `json:"file_sha1"`
	VirusName   string `json:"virus_name"`
	VirusSize   int64  `json:"virus_size"`
	Category    int64  `json:"category"`
}

type Request struct {
	Category  string `json:"category" mapstructure:"category"`
	FileName  string `json:"file_name" mapstructure:"file_name" binding:"required"`
	Md5       string `json:"md5" mapstructure:"md5" binding:"required"`
	Sha1      string `json:"sha1" mapstructure:"sha1" binding:"required"`
	VirusName string `json:"virus_name" mapstructure:"virus_name"`
	VirusSize string `json:"virus_size" mapstructure:"virus_size"`
	FilePath  string `json:"file_path" mapstructure:"file_path" binding:"required"`
	RootUUID  string `json:"root_uuid" mapstructure:"root_uuid"`
	CheckTime string `json:"check_time" mapstructure:"check_time"`
}
