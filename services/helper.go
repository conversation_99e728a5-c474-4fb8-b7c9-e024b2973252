package services

import (
	"context"
	"encoding/json"
	baseErrors "errors"
	"strconv"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"rm.git/client_api/rm_common_libs.git/v2/library/cipher"
	"rm.git/client_api/rm_common_libs.git/v2/library/errors"
	"rm.git/client_api/rm_common_libs.git/v2/library/utils"

	"rongma.com/srms/models"
	"rongma.com/srms/modules/client"
	"rongma.com/srms/modules/consts"
	"rongma.com/srms/modules/logger"
	"rongma.com/srms/repository"
	"rongma.com/srms/rmgrpc/rmmd5"
)

func Decrypt(ctx context.Context, data []byte, key string, cipherSuite string, channel string) (string, error) {
	defer func() {
		if err := recover(); err != nil {
			logger.WithContext(ctx).Errorf("%v", err)
			channelCacheKey := consts.GetChannelCacheKey(channel)
			// 出错后清理缓存
			client.Redis.Del(ctx, channelCacheKey)
			logger.WithContext(ctx).Errorf("clear channel cache %s,%v", channelCacheKey, err)
		}
	}()

	var (
		suite *cipher.Suite
		err   error
	)

	if suite, err = cipher.GetSuite(cipherSuite); err != nil {
		return "", err
	}

	secretKey, err := utils.Base64Decode(key)
	if err != nil {
		return "", err
	}

	decrypted, err := suite.Aes.Decrypt(data, secretKey, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("---error: %v", err)
		return "", errors.ContentFormatNotMatch
	}

	return strings.Trim(string(decrypted), "\r\n"), nil
}

// GetLevel 根据md5（必填） 和sha1（可选） 获取文件的等级
func GetLevel(ctx context.Context, md5 string, sha1 string, fileName string, Funcs ...func(sha1 string) error) (int, map[string]interface{}, bool) {
	var (
		level      = 0
		isFoundQax = true //是否从奇安信查找到等级
		extend     = make(map[string]interface{}, 2)
		res        = new(models.FileCacheValue)
		duration   = time.Hour
	)

	for _, Func := range Funcs {
		if err := Func(sha1); err != nil {
			return level, extend, isFoundQax
		}
	}

	if len(md5) == 0 {
		logger.WithContext(ctx).Errorf("[query] getLevel md5 is null")
		return level, extend, isFoundQax
	}

	// 从cache中查看文件的level等级和extend的数据
	fileLevelCacheKey := consts.GetFileLevelCacheKey(md5)

	resp, redisErr := client.Redis.Get(ctx, fileLevelCacheKey).Result()
	if redisErr == nil && resp != "" {
		jsonErr := json.Unmarshal([]byte(resp), res)
		if jsonErr != nil {
			logger.WithContext(ctx).Errorf("[query] getLevel from redis value is not right ,err: %s", jsonErr.Error())
			redisErr = jsonErr
		}
	}

	if redisErr == nil && res != nil {
		return res.Level, res.Extend, isFoundQax
	}

	//  如果redis 不存在或者解析出问题，那么相应的去调用rpc的方式去获取

	// RPC 调用然后去处理相关的数据写入redis
	md5Info, err := client.RmMd5.GetMd5Info(ctx, &rmmd5.GetMd5InfoRequest{
		RequestCon: &rmmd5.RequestCon{
			Md5: md5, Sha1: sha1, FileName: fileName,
		},
		IsDirect: false,
	})

	if err != nil {
		logger.WithContext(ctx).Errorf("[query] grpc found md5 info error: %s, md5: %s, sha1: %s, level: %d ", err, md5, sha1, level)
		return level, extend, isFoundQax
	}
	// 获取数据
	if malWareName := md5Info.GetData().GetSecurity().GetHasMalware().GetMalware().GetName(); malWareName != "" {
		extend["malware_name"] = malWareName
	}

	if malWareClass := md5Info.GetData().GetSecurity().GetHasMalware().GetMalware().GetClass(); malWareClass != "" {
		extend["malware_class"] = malWareClass
	}

	if md5Info.Code == 99999 {
		level = 40
		// 调用第三方接口没有找到对应的数据
		logger.WithContext(ctx).Warnf("[query] grpc not found md5 info, md5: %s, sha1: %s, level: %d ", md5, sha1, level)
		// 未找到暂时缓存5分钟
		duration = 5 * time.Minute
		isFoundQax = false
	} else {
		level = int(md5Info.GetData().GetSecurity().GetLevel())
		logger.WithContext(ctx).Infof("[query] grpc found md5: %s, level: %d", md5, level)
	}

	fileCacheBytes, _ := json.Marshal(models.FileCacheValue{
		Level:  level,
		Extend: extend,
	})

	client.Redis.Set(ctx, fileLevelCacheKey, string(fileCacheBytes), duration)

	return level, extend, isFoundQax
}

// NeedUpload 判断文件是否需要上传
func NeedUpload(ctx context.Context, md5 string, sha1 string, sha256 string) int {
	var (
		needUpload        = 0
		fileExistCacheKey = consts.GetFileExistCacheKey(md5)
		baseVal           = "1"
	)

	if len(md5) == 0 {
		logger.WithContext(ctx).Errorf("[query] getLevel md5 is null")
		return needUpload
	}
	// 从cache中查看文件是否要上传
	fileExist, err := client.Redis.Exists(ctx, fileExistCacheKey).Result()
	if err == nil && fileExist > 0 {
		return needUpload
	}
	// 缓存中不存在，则读取数据库
	fileInfo, err := repository.NewSRMSService().OnQueryFileSecurity(ctx, md5, sha1, sha256) // 调用上传方法，实现功能逻辑
	if err != nil {
		logger.WithContext(ctx).Errorf("[query] query file security error, %s", err)
		return needUpload
	}

	if fileInfo.NeedUpload != 0 {
		needUpload = 1
		return needUpload
	}

	_, err = client.Redis.Set(ctx, fileExistCacheKey, baseVal, time.Hour).Result()
	if err != nil {
		logger.WithContext(ctx).Errorf("[query] redis write error, key: %s, value: %s, err: %s", fileExistCacheKey, baseVal, err)
	}

	return needUpload
}

func ReachedThreshold(ctx context.Context, clientId string, partner string) bool {

	clientThresholdKey := consts.GetClientThresholdKey(clientId)
	valStr, redisErr := client.Redis.Get(ctx, clientThresholdKey).Result()

	if redisErr != nil {
		// key 不存在，去获取规则，并生成具体的规则
		if !baseErrors.Is(redisErr, redis.Nil) {
			logger.WithContext(ctx).Errorf("get client threshold cache error, key: %s, error: %s", clientThresholdKey, redisErr)
			return true
		}
		// 从配置文件读取上传频率
		partnerThreshold, err := getPartnerThresholdFromConfig(ctx)
		if err != nil {
			logger.WithContext(ctx).Warnf("get partner threshold cache error: %s, partner:  %s", partner, err)
			// 获取失败，则采用默认规则,1分钟30次
			_, err := client.Redis.Set(ctx, clientThresholdKey, 30, time.Minute).Result()
			if err != nil {
				logger.WithContext(ctx).Errorf("use default client threshold error, key: %s, error: %s", clientThresholdKey, err)
				return true
			}
			logger.WithContext(ctx).Warnf("use default client threshold, partner: %s", partner)

			return false
		}

		logger.WithContext(ctx).Infof("partner threshold info, partner: %s, limit: %d, expire_minute: %d minutes",
			partner, partnerThreshold.Limit, partnerThreshold.ExpiresMinute)

		// 根据查询到的数据生成针对channelId的规则
		_, setErr := client.Redis.Set(ctx, clientThresholdKey, partnerThreshold.Limit,
			time.Minute*time.Duration(partnerThreshold.ExpiresMinute)).Result()

		if setErr != nil {
			logger.WithContext(ctx).Errorf("set client threshold cache error, key: %s, limit: %d, expire_minute: %d, error: %v",
				clientThresholdKey, partnerThreshold.Limit, partnerThreshold.ExpiresMinute, setErr)

			return true
		}

		return false
	}

	val, err := strconv.Atoi(valStr)
	if err != nil || val < 1 {
		logger.WithContext(ctx).Errorf("get key: %s, reachedThreshold convert error or val: %d ,letter than 1", clientThresholdKey, val)
		return true
	}

	// 减值处理
	descRes, err := client.Redis.Decr(ctx, clientThresholdKey).Result()
	if err != nil {
		logger.WithContext(ctx).Errorf("clientThresholdKey decr error: %s", err)
		return true
	}

	//  排除键已过期
	if descRes < 0 {
		ttlResult, err := client.Redis.TTL(ctx, clientThresholdKey).Result()
		if err != nil {
			logger.WithContext(ctx).Errorf("failed to call TTL error: %s", err)
			return true
		}
		// 判断是否永久存在，永久存在的结果为-1
		if ttlResult.Seconds() == -1 {
			client.Redis.Del(ctx, clientThresholdKey)
		}
	}

	return false

}

func getPartnerThresholdFromConfig(ctx context.Context) (*models.Threshold, error) {

	limit := conf.Service.ThresholdLimit
	minute := conf.Service.ThresholdMinute

	if limit < 1 || minute < 1 {
		logger.WithContext(ctx).Errorf("get partner threshold from config error, limit: %d, minute: %d", limit, minute)
		return nil, errors.InvalidParameter
	}

	partnerThreshold := &models.Threshold{Limit: limit, ExpiresMinute: minute}

	return partnerThreshold, nil

}
