package services

import (
	"context"
	"time"

	"rm.git/client_api/rm_common_libs.git/v2/common/protobuf/portal"

	"rongma.com/srms/models"
)

type ISRMSService interface {
	DecodeRequestBody(ctx context.Context, channel string, respMessage *portal.AcquireChannelResponse, body []byte) (string, error)
	OnQueryFileSecurity(ctx context.Context, body map[string]string, auth *models.RequestAuth) (*models.FileSecurityQueryResponse, error)
	OnUpload(ctx context.Context, body map[string]string, auth *models.RequestAuth) (*models.FileUploadResponse, error)
	OnUploadShard(ctx context.Context, fileData, fileToken string, auth *models.RequestAuth) (*models.FileShardUploadResponse, error)
}

type IIsolateFileService interface {
	GetCustomLevel(ctx context.Context, orgName, hash, clientId string) (int, error)
	InsertIsolateFile(ctx context.Context, document interface{}) error
	FindIsolateFileByOne(ctx context.Context, orgName string, md5 string, sha1 string, clientID string, filePath string) (*models.IsolateFile, error)
	UpdateIsolateFileTime(ctx context.Context, guid string, timeNow time.Time) error

	InsertIsolateFileExt(ctx context.Context, document interface{}) error
	IsExistRootUUIDByIsolateFileExt(ctx context.Context, isolateFileID string, rootUUid string) (bool, error)
	FindIsolateFileExt(ctx context.Context, isolateFileID string, rootUuid string) (*models.IsolateFileExt, error)
	UpdateIsolateFileExtTime(ctx context.Context, isolateFileID string, rootUuid string, timeNow time.Time) error
}
