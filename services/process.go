package services

import (
	"context"
	"encoding/json"
	baseErrors "errors"
	"fmt"
	"strconv"

	"github.com/redis/go-redis/v9"
	"rm.git/client_api/rm_common_libs.git/v2/library/errors"

	"rongma.com/srms/config"
	"rongma.com/srms/models"
	"rongma.com/srms/modules/client"
	"rongma.com/srms/modules/consts"
	"rongma.com/srms/modules/logger"
	"rongma.com/srms/repository"
	"rongma.com/srms/rmgrpc/rmmd5"
	"rongma.com/srms/utils"

	"github.com/mitchellh/mapstructure"
)

var conf = config.Config()

type SRMSProcessV2 struct {
	service repository.ImplSRMS
}

func NewSRMSProcessV2(service repository.ImplSRMS) SRMSProcessV2 {
	return SRMSProcessV2{service: service}
}

func (s SRMSProcessV2) baseOnQueryFileSecurity(ctx context.Context, channelId string, rmRequestData *models.RMRequestData) (*models.FileSecurityQueryResponse, error) {
	clientId := rmRequestData.Token.ClientId
	orgName := rmRequestData.Token.OrgName
	logger.WithContext(ctx).Infof("[query] client_id: %s, channelId: %s ", clientId, channelId)

	modelRequest := &models.FileSecurityQueryRequest{}
	if err := mapstructure.Decode(rmRequestData.Data, modelRequest); err != nil {
		//IOResponse(ctx, err, &channelAcquire)
		return nil, err
	}

	modelRequest = utils.FileSecurityFieldCompatible(modelRequest)

	if len(modelRequest.MD5) == 0 {
		logger.WithContext(ctx).Errorf("[query] md5 is null, request: %+v", modelRequest)
		return nil, errors.InvalidParameter
	}

	fileName := utils.GetFileName(rmRequestData.Data)

	var (
		level, needUpload         int
		commandLineFileLevel      int
		commandLineFileNeedUpload int
		extend                    map[string]interface{}
		isFoundQax                bool
	)
	ignoreIsFoundQax := conf.Service.IgnoreIsFoundQax
	level, extend, isFoundQax = GetLevel(ctx, modelRequest.MD5, modelRequest.SHA1, fileName)

	if conf.Service.UploadSwitch == "on" && (isFoundQax == false || ignoreIsFoundQax) { //上传开关开启&&(奇安信找不到等级||忽略奇安信返回)
		needUpload = NeedUpload(ctx, modelRequest.MD5, modelRequest.SHA1, modelRequest.SHA256)
	}

	if modelRequest.CommandLineFile != "" &&
		(modelRequest.CommandLineFileMd5 != "" || modelRequest.CommandLineFileSha1 != "") {
		commandLineFileLevel, extend, isFoundQax = GetLevel(ctx, modelRequest.CommandLineFileMd5, modelRequest.CommandLineFileSha1, fileName)

		if conf.Service.UploadSwitch == "on" && (isFoundQax == false || ignoreIsFoundQax) { //上传开关开启&&(奇安信找不到等级||忽略奇安信返回)
			commandLineFileNeedUpload = NeedUpload(ctx, modelRequest.CommandLineFileMd5, modelRequest.CommandLineFileSha1,
				modelRequest.CommandLineFileSha256)
		}
	}

	// 返回结果
	res := &models.FileSecurityQueryResponse{
		MD5:                       modelRequest.MD5,
		SHA1:                      modelRequest.SHA1,
		SHA256:                    modelRequest.SHA256,
		Level:                     level,
		NeedUpload:                needUpload,
		CommandLineFileMD5:        modelRequest.CommandLineFileMd5,
		CommandLineFileSHA1:       modelRequest.CommandLineFileSha1,
		CommandLineFileLevel:      commandLineFileLevel,
		CommandLineFileNeedUpload: commandLineFileNeedUpload,
		Extend:                    extend,
	}

	if utils.IntInArray(conf.Service.CheckLevel, level) {

		if rmRequestData.Data["s_operation"] == "loadimage" {
			rmRequestData.Data["i_newimagelevel"] = strconv.Itoa(level)
		} else if rmRequestData.Data["s_operation"] == "createprocess" {
			rmRequestData.Data["i_newprocesslevel"] = strconv.Itoa(level)
			rmRequestData.Data["i_commandlinefilelevel"] = strconv.Itoa(commandLineFileLevel)
		}

	}

	if len(fileName) != 0 {
		if 60 <= level && level <= 70 {
			_ = s.service.OnSaveMaliceFileInfo(ctx, res, fileName, orgName)
		}
	}

	return res, nil

}

// OnUpload 样本文件上传请求
func (s SRMSProcessV2) baseOnUpload(ctx context.Context, channelId string, rmRequestData *models.RMRequestData) (*models.FileUploadResponse, error) {
	res := &models.FileUploadResponse{}
	clientId := rmRequestData.Token.ClientId
	partner := rmRequestData.Token.OrgName // todo 合作者id，用于后期可以根据客户来配置上传频率

	logger.WithContext(ctx).Infof("[upload] client_id: %s, channelId: %s ", clientId, channelId)

	modelRequest := &models.FileUploadRequest{}
	// 涉及到string转int64所以使用WeakDecode
	if err := mapstructure.WeakDecode(rmRequestData.Data, modelRequest); err != nil {
		logger.WithContext(ctx).Errorf("err: %s", modelRequest)
		return nil, err
	}

	modelRequest.OrgName = rmRequestData.Token.OrgName
	modelRequest.ClientId = rmRequestData.Token.ClientId

	if modelRequest.MD5 == "" {
		logger.WithContext(ctx).Errorf("[upload] parameter error, md5:%s", modelRequest.MD5)
		return nil, errors.InvalidParameter
	}

	// md5 已经存在则直接返回
	fileMd5CacheKey := consts.GetFileExistCacheKey(modelRequest.MD5)
	count, redisErr := client.Redis.Exists(ctx, fileMd5CacheKey).Result()
	// key存在或缓存出错，都返回不上传样本
	if count > 0 || redisErr != nil {
		logger.WithContext(ctx).Errorf("this clientId %s key: %s value: %s", clientId, fileMd5CacheKey, count)
		return res, nil
	}

	// 如果当前channelId超过上传限制，则直接返回
	if ReachedThreshold(ctx, clientId, partner) {
		logger.WithContext(ctx).Errorf("this clientId %s is reachedThreshold already", clientId)
		return res, nil
	}

	logger.WithContext(ctx).Infof("[upload] upload request info: %+v", modelRequest)

	res, err := s.service.OnUpload(ctx, modelRequest) // 调用上传方法，实现功能逻辑
	if err != nil {
		return nil, err
	} else {
		return res, nil
	}

}

// OnUploadShard 样本文件上传
func (s SRMSProcessV2) baseOnUploadShard(ctx context.Context, channelId string, fileToken string, rmRequestData *models.RMRequestData) (*models.FileShardUploadResponse, error) {
	res := &models.FileShardUploadResponse{}
	clientId := rmRequestData.Token.ClientId
	partnerId := rmRequestData.Token.OrgName // todo 合作者id，用于后期可以根据客户来配置上传频率

	logger.WithContext(ctx).Infof("[shard] partner_id: %s, channel: %s, client_id: %s, file_token: %s", partnerId, channelId, clientId, fileToken)

	// 如果当前channelId超过上传限制，则直接返回
	if ReachedThreshold(ctx, clientId, partnerId) {
		return res, nil
	}

	modelRequest := &models.FileShardUploadRequest{}

	modelRequest.Token = fileToken

	parseContentStr, ok := rmRequestData.Data["body"]
	if !ok {
		return nil, errors.InvalidParameter
	}

	modelRequest.Data = parseContentStr

	if modelRequest.Data == "" || modelRequest.Token == "" {
		logger.WithContext(ctx).Errorf("[shard] parameter error, token:%s, data length:%s", modelRequest.Token, len(modelRequest.Data))
		return nil, errors.InvalidParameter
	}

	res, err := s.service.OnUploadShard(ctx, modelRequest) // 调用上传方法，实现功能逻辑
	if err != nil {
		logger.WithContext(ctx).Errorf("[shard] upload shard error:%s", err)
		return nil, err
	} else {
		logger.WithContext(ctx).Infof("[shard] result:%+v", res)
		return res, nil
	}

}

// OnBatchQueryFileSecurity 批量获取文件安全等级，参数列表长度最大30
func (s SRMSProcessV2) baseOnBatchQueryFileSecurity(ctx context.Context, channelId string, rmRequestData *models.RMRequestData) (map[string]interface{}, error) {
	clientId := rmRequestData.Token.ClientId
	orgName := rmRequestData.Token.OrgName
	logger.WithContext(ctx).Infof("[batch_query] client_id: %s, channelId: %s, orgName: %s", clientId, channelId, orgName)

	body, ok := rmRequestData.Data["body"]
	if !ok {
		logger.WithContext(ctx).Errorf("[batch_query] body is null, client_id: %s, channelId: %s, orgName: %s", clientId, channelId, orgName)
		return nil, errors.InvalidParameter
	}

	modelRequest := &models.FileSecurityBatchQueryRequest{}
	err := json.Unmarshal([]byte(body), &modelRequest)
	if err != nil {
		return nil, err
	}

	if len(modelRequest.QueryList) > consts.MaxQueryListLength {
		return nil, errors.New(230, fmt.Sprintf("query list size too long, max size %d", consts.MaxQueryListLength))
	}
	resBaseParamList := make([]models.BatchQueryBaseParam, 0, len(modelRequest.QueryList))

	batchRequest := &rmmd5.BatchGetMd5InfoRequest{}
	for _, v := range modelRequest.QueryList {
		batchRequest.RequestCon = append(batchRequest.RequestCon, &rmmd5.RequestCon{
			Md5:  v.Md5,
			Sha1: v.Sha1,
		})
		resBaseParamList = append(resBaseParamList, models.BatchQueryBaseParam{Md5: v.Md5, Sha1: v.Sha1})
	}

	batchInfo, err := client.RmMd5.BatchGetMd5Info(context.Background(), batchRequest)
	if err != nil {
		logger.WithContext(ctx).Errorf("[batch_query] grpc found md5 info error: %s, query list: %+v ", err, modelRequest.QueryList)
		return nil, err
	}

	if batchInfo.GetData() == nil {
		logger.WithContext(ctx).Errorf("[batch_query] grpc found md5 info error, data is null query list: %+v", modelRequest.QueryList)
		return nil, errors.InvalidParameter
	}

	for i, v := range batchInfo.GetData() {
		resBaseParamList[i].Level = int(v.Security.Level)
	}

	res := make(map[string]interface{}, 1)
	res["query_result"] = resBaseParamList

	return res, nil

}

func (s SRMSProcessV2) getPartnerThresholdCache(ctx context.Context, partner string) (*models.Threshold, error) {
	partnerKey := consts.GetPartnerKey(partner)

	threshold := &models.Threshold{}
	partnerThresholdJsonStr, err := client.Redis.Get(ctx, partnerKey).Result()

	if err != nil {
		if !baseErrors.Is(err, redis.Nil) {
			logger.WithContext(ctx).Infof("get partnerKey:%s value error:%s", partnerKey, err)
			return nil, err
		}

		logger.WithContext(ctx).Infof("partnerKey:%s cache value is lost, get rule form db...", partnerKey)
		thresholdInfo, err := s.service.OnQueryThreshold(ctx, partner)
		if err != nil {
			logger.WithContext(ctx).Errorf("get partner:%s cache rule info error, %s", partner, err)
			return nil, err
		}

		partnerThresholdByte, err := json.Marshal(thresholdInfo)
		if err != nil {
			logger.WithContext(ctx).Errorf("getPartnerThresholdCache Marshal error:%s, partnerKey:%s", err, partnerKey)
			return nil, err
		}

		_, setErr := client.Redis.Set(ctx, partnerKey, string(partnerThresholdByte), 0).Result()
		if setErr != nil {
			logger.WithContext(ctx).Errorf("set partnerKey:%s value error:%s, limit:%s, expireMinute:%s,",
				partnerKey, setErr, thresholdInfo.Limit, thresholdInfo.ExpiresMinute)
			return nil, setErr
		}
		logger.WithContext(ctx).Infof("set partnerKey:%s value success, limit:%s, expireMinute:%s",
			partnerKey, thresholdInfo.Limit, thresholdInfo.ExpiresMinute)

		return thresholdInfo, nil

	}

	if err := json.Unmarshal([]byte(partnerThresholdJsonStr), &threshold); err != nil {
		logger.WithContext(ctx).Errorf("getPartnerThresholdCache json unmarshal error:%s", err)
		return nil, err
	}

	return threshold, nil
}
