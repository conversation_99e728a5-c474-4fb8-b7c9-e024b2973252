package services

import (
	"context"
	"encoding/json"
	baseErr "errors"
	"strings"
	"time"

	rmgroup "rm.git/cloud_api/rm_common_protos.git/proto_go/rmgroup/v1"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	srmsErr "rongma.com/srms/errors"
	"rongma.com/srms/models"
	"rongma.com/srms/modules/client"
	"rongma.com/srms/modules/consts"
	"rongma.com/srms/modules/logger"
)

var emptyMap, _ = json.Marshal(map[int64]int{})

type IsolateFileService struct {
	iocCollection            *mongo.Collection
	isolateFileCollection    *mongo.Collection
	isolateFileExtCollection *mongo.Collection
}

func NewIsolateFileService() *IsolateFileService {
	return &IsolateFileService{
		isolateFileCollection:    client.MongoSrms.Database.Collection("isolate_file"),
		iocCollection:            client.MongoEngines.Database.Collection("ioc_exclusion"),
		isolateFileExtCollection: client.MongoSrms.Database.Collection("isolate_file_ext"),
	}
}

func (s *IsolateFileService) GetCachedData(ctx context.Context, cacheKey string, groupIds []int64) (int, error) {
	var level = 0
	redisValue := make(map[int64]int, 5)
	redisRes, err := client.Redis.Get(ctx, cacheKey).Result()
	if err != nil {
		logger.WithContext(ctx).Warnf("get key: %s, error: %s", cacheKey, err)
		return level, err
	}

	if redisRes == string(emptyMap) {
		logger.WithContext(ctx).Infof("get key: %s, value: %s", cacheKey, redisRes)
		return level, nil
	}

	unmarshalErr := json.Unmarshal([]byte(redisRes), &redisValue)
	if unmarshalErr != nil {
		logger.WithContext(ctx).Warnf("failed to unmarshal key: %s, data: %s, error: %s", cacheKey, redisRes, unmarshalErr)
		return level, unmarshalErr
	}

	//  如果是全网主机，默认直接返回
	if data, exists := redisValue[0]; exists {
		return data, nil
	}

	// 分组形式的话，需要处理辨别groupId
	for _, groupId := range groupIds {
		if data, exists := redisValue[groupId]; exists {
			level = data
			break
		}
	}

	return level, nil

}

//nolint:funlen
func (s *IsolateFileService) GetCustomLevel(ctx context.Context, orgName, hash, clientId string) (int, error) {
	var (
		key   = consts.GetCustomLevel(orgName, hash)
		err   error
		level = 0
	)

	result, err := client.GroupClient.GetGroupIDListByMember(ctx, &rmgroup.GetGroupIDListByMemberRequest{Member: clientId, OrgName: orgName})
	if err != nil {
		logger.WithContext(ctx).Errorf("[isolate] GetCustomLevel call rpc GetGroupIDListByMember error: %s", err)
		return level, err
	}

	// 缓存中获取数据
	level, err = s.GetCachedData(ctx, key, result.GetData().GetGroupIds())
	if err == nil {
		logger.WithContext(ctx).Infof("cacheKey: %s already exists,org_name: %s, hash: %s, client_id: %s ", key, orgName, hash, clientId)
		return level, nil
	}

	var isolateFile = &models.IsolateFileLevel{}
	// 查询数据库的数据
	filter := bson.M{"org_name": orgName, "hash": strings.ToUpper(hash), "$or": []bson.M{{"expiration_date": bson.M{"$gt": time.Now().Unix()}}, {"expiration_date": 0}}}
	err = s.iocCollection.FindOne(ctx, filter, options.FindOne()).Decode(&isolateFile)
	if err != nil {
		if !baseErr.Is(err, mongo.ErrNoDocuments) {
			logger.WithContext(ctx).Errorf("failed to find one document, filter: %+v, error: %s", filter, err)
		}
		return 0, err
	}

	redisValue := make(map[int64]int, len(isolateFile.GroupIds)+1)

	if isolateFile.HostType == consts.HostTypeAll {
		redisValue[0] = isolateFile.Level
		level = isolateFile.Level
	} else {
		for _, groupId := range isolateFile.GroupIds {
			redisValue[groupId] = isolateFile.Level
		}

		for _, groupId := range result.GetData().GetGroupIds() {
			if data, exists := redisValue[groupId]; exists {
				level = data
				break
			}
		}
	}

	if len(redisValue) > 0 {
		//  缓存正确的数据
		jsonByte, _ := json.Marshal(redisValue)
		client.Redis.Set(ctx, key, string(jsonByte), time.Minute*5)
	} else {
		client.Redis.Set(ctx, key, string(emptyMap), time.Minute*2) //先提前缓存 “ ” 缓存的可能是没有值的数据，空值 redis proxy 会报错
	}

	return level, nil
}
func (s *IsolateFileService) InsertIsolateFile(ctx context.Context, document interface{}) error {
	insertRes, err := s.isolateFileCollection.InsertOne(ctx, document)
	if err != nil {
		logger.WithContext(ctx).Errorf("[isolate] insert one error: %s", err)
		return err
	}
	logger.WithContext(ctx).Infof("[isolate] insert one document is: %+v,result: %s", document, insertRes.InsertedID)
	return nil
}

func (s *IsolateFileService) FindIsolateFileByOne(ctx context.Context, orgName string, md5 string, sha1 string, clientId string, filePath string) (*models.IsolateFile, error) {
	filter := bson.M{"org_name": orgName, "md5": md5, "sha1": sha1, "client_id": clientId, "file_path": filePath}

	opts := &options.FindOptions{}
	opts.SetSort(bson.D{bson.E{Key: "create_time", Value: -1}})

	isolateFileList, err := s.FindMany(ctx, filter, opts)
	if err != nil {
		logger.WithContext(ctx).Errorf("[isolate] FindIsolateFileByOne error: %s", err)
		return nil, err
	}
	if len(isolateFileList) > 0 {
		return isolateFileList[0], nil
	}
	return nil, nil
}
func (s *IsolateFileService) UpdateIsolateFileTime(ctx context.Context, guid string, timeNow time.Time) error {
	filter := bson.M{"guid": guid}
	update := bson.M{
		"$set": bson.M{
			"last_quarantine_time": timeNow.Unix(),
		},
	}
	_, err := s.isolateFileCollection.UpdateMany(context.Background(), filter, update)
	if err != nil {
		logger.WithContext(ctx).Errorf("[isolate] UpdateIsolateFileTime error: %s", err)
		return err
	}
	return nil
}

func (s *IsolateFileService) FindMany(ctx context.Context, filter interface{}, opt *options.FindOptions) ([]*models.IsolateFile, error) {

	cursor, err := s.isolateFileCollection.Find(ctx, filter, opt)

	if err != nil {
		return nil, err
	}

	defer func(cursor *mongo.Cursor, ctx context.Context) {
		_ = cursor.Close(ctx)
	}(cursor, ctx)

	data := make([]*models.IsolateFile, 0)

	if err = cursor.All(ctx, &data); err != nil {
		return nil, err
	}
	return data, nil
}
func (s *IsolateFileService) InsertIsolateFileExt(ctx context.Context, document interface{}) error {
	insertRes, err := s.isolateFileExtCollection.InsertOne(ctx, document)
	if err != nil {
		logger.WithContext(ctx).Errorf("[isolate.IsolateFileExt] insert one error: %s", err)
		return err
	}
	logger.WithContext(ctx).Infof("[isolate.IsolateFileExt] insert one document is: %+v,result: %v", document, insertRes.InsertedID)
	return nil
}
func (s *IsolateFileService) IsExistRootUUIDByIsolateFileExt(ctx context.Context, isolateFileId string, rootUuid string) (bool, error) {
	isolateFileExt := &models.IsolateFileExt{}

	filter := bson.M{"isolate_file_id": isolateFileId, "root_uuid": rootUuid}
	if err := s.isolateFileExtCollection.FindOne(ctx, filter).Decode(&isolateFileExt); err != nil {
		if !baseErr.Is(err, mongo.ErrNoDocuments) {
			logger.WithContext(ctx).Errorf("[isolate] find one err:%s, filter:%s", err, filter)
			return false, srmsErr.MongoQueryEmpty
		}
	}
	if isolateFileExt.IsolateFileId == "" {
		return false, nil
	}
	return true, nil
}

func (s *IsolateFileService) FindIsolateFileExt(ctx context.Context, isolateFileId string, rootUuid string) (*models.IsolateFileExt, error) {
	isolateFileExt := &models.IsolateFileExt{}
	filter := bson.M{"isolate_file_id": isolateFileId, "root_uuid": rootUuid}
	if err := s.isolateFileExtCollection.FindOne(ctx, filter).Decode(&isolateFileExt); err != nil {
		if !baseErr.Is(err, mongo.ErrNoDocuments) {
			logger.WithContext(ctx).Errorf("[isolate] find one err:%s, filter:%s", err, filter)
			return nil, srmsErr.MongoQueryEmpty
		}
	}

	return isolateFileExt, nil
}

func (s *IsolateFileService) UpdateIsolateFileExtTime(ctx context.Context, isolateFileId string, rootUuid string, timeNow time.Time) error {
	filter := bson.M{"isolate_file_id": isolateFileId, "root_uuid": rootUuid}
	update := bson.M{
		"$set": bson.M{
			"last_update_time": timeNow.Unix(),
		},
	}
	_, err := s.isolateFileExtCollection.UpdateMany(context.Background(), filter, update)
	if err != nil {
		logger.WithContext(ctx).Errorf("[isolate] UpdateIsolateFileExtTime error: %s", err)
		return err
	}
	return nil
}
