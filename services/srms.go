package services

import (
	"context"
	"encoding/json"
	"time"

	"github.com/spf13/cast"
	"rm.git/client_api/rm_common_libs.git/v2/common/protobuf/portal"
	rmCommonInterfaces "rm.git/client_api/rm_common_libs.git/v2/interfaces"
	"rm.git/client_api/rm_common_libs.git/v2/library/errors"

	"rongma.com/srms/models"
	"rongma.com/srms/modules/client"
	"rongma.com/srms/modules/consts"
	"rongma.com/srms/modules/logger"
	"rongma.com/srms/repository"
)

type SRMSService struct {
	redis       rmCommonInterfaces.RedisClient
	srmsProcess SRMSProcessV2
}

func NewSRMSService() ISRMSService {
	return &SRMSService{
		redis:       client.Redis,
		srmsProcess: NewSRMSProcessV2(repository.NewSRMSService()),
	}
}

func (s *SRMSService) OnQueryFileSecurity(ctx context.Context, body map[string]string, auth *models.RequestAuth) (*models.FileSecurityQueryResponse, error) {

	rMRequestData := &models.RMRequestData{
		Token: &models.RMToken{
			ClientId:   auth.ClientID,
			TokenKeyId: "",
			OrgName:    auth.OrgName},
		Session: &models.RMSession{},
		Data:    body}
	resp, err := s.srmsProcess.baseOnQueryFileSecurity(ctx, auth.Channel, rMRequestData)
	if err != nil {
		logger.WithContext(ctx).Error("%s", err)
		return nil, err
	}

	response := &models.FileSecurityQueryResponse{
		MD5:                       resp.MD5,
		SHA1:                      resp.SHA1,
		SHA256:                    resp.SHA256,
		Level:                     cast.ToInt(resp.Level),
		NeedUpload:                cast.ToInt(resp.NeedUpload),
		CommandLineFileMD5:        resp.CommandLineFileMD5,
		CommandLineFileSHA1:       resp.CommandLineFileSHA1,
		CommandLineFileLevel:      cast.ToInt(resp.CommandLineFileLevel),
		CommandLineFileNeedUpload: cast.ToInt(resp.CommandLineFileNeedUpload),
		Extend:                    resp.Extend,
	}

	if resp.NeedUpload == 1 && resp.SHA1 != "" {
		key := consts.GetMd5RelationSha1Key(resp.MD5)
		client.Redis.Set(ctx, key, resp.SHA1, time.Minute*5)
	}
	if resp.CommandLineFileNeedUpload == 1 && resp.CommandLineFileSHA1 != "" {
		key := consts.GetMd5RelationSha1Key(resp.CommandLineFileMD5)
		client.Redis.Set(ctx, key, resp.CommandLineFileSHA1, time.Minute*5)
	}

	return response, nil
}

func (s *SRMSService) OnUpload(ctx context.Context, body map[string]string, auth *models.RequestAuth) (*models.FileUploadResponse, error) {
	// 兼容处理，老版客户端不上传sha1时,根据security接口中记录的md5与sha1的对应关系从redis中获取，并补充到body中
	if _, ok := body["sha1"]; !ok {
		if md5, ok := body["md5"]; ok {
			key := consts.GetMd5RelationSha1Key(md5)
			sha1, err := client.Redis.Get(ctx, key).Result()
			if err != nil {
				logger.WithContext(ctx).Errorf("not found md5 relation sha1, redis key: %s, err: %s", key, err)
				return nil, err
			}
			body["sha1"] = sha1
		}
	}

	rMRequestData := &models.RMRequestData{
		Token: &models.RMToken{
			ClientId:   auth.ClientID,
			TokenKeyId: "",
			OrgName:    auth.OrgName},
		Session: &models.RMSession{},
		Data:    body}

	upload, err := s.srmsProcess.baseOnUpload(ctx, auth.Channel, rMRequestData)
	if err != nil {
		logger.WithContext(ctx).Errorf("failed to call srms.OnUpload error: %s", err)
		return nil, err
	}

	return &models.FileUploadResponse{
		Size:   upload.Size,
		Offset: upload.Offset,
		Token:  upload.Token,
	}, nil
}

func (s *SRMSService) OnUploadShard(ctx context.Context, fileData, fileToken string, auth *models.RequestAuth) (*models.FileShardUploadResponse, error) {
	rMRequestData := &models.RMRequestData{
		Token: &models.RMToken{
			ClientId:   auth.ClientID,
			TokenKeyId: "",
			OrgName:    auth.OrgName},
		Session: &models.RMSession{},
		Data:    map[string]string{"body": fileData},
	}

	upload, err := s.srmsProcess.baseOnUploadShard(ctx, auth.Channel, fileToken, rMRequestData)
	if err != nil {
		logger.WithContext(ctx).Errorf("failed to call onUploadShard error: %s", err)
		return nil, err
	}

	return &models.FileShardUploadResponse{
		Size:   upload.Size,
		Offset: upload.Offset,
		Token:  upload.Token,
	}, nil
}

func (s *SRMSService) DecodeRequestBody(ctx context.Context, channel string, resp *portal.AcquireChannelResponse, body []byte) (string, error) {
	var (
		retry           = false
		acq             = models.AcquireChannel{}
		channelCacheKey = consts.GetChannelCacheKey(channel) // 修改为读取portal业务中的缓存key
	)

	// 先从redis中获取，获取失败再连线获取
	info, err := s.redis.Get(ctx, channelCacheKey).Result()
	if err != nil || info == "" {
		retry = true
	} else {
		if err := json.Unmarshal([]byte(info), &acq); err != nil {
			retry = true
		} else {
			resp.SecretKey = acq.SecretKey
			resp.CipherSuite = acq.CipherSuite
		}
	}

	if retry {
		// grpc的方案
		res, err := client.Portal.AcquireChannel(ctx, &portal.AcquireChannelRequest{Channel: channel})
		if err != nil {
			logger.WithContext(ctx).Errorf("[decode request]  request portal err: %v", err)
			return "", errors.InvalidChannel
		}

		resp.SecretKey = res.GetSecretKey()
		resp.CipherSuite = res.GetCipherSuite()
	}

	logs, err := Decrypt(ctx, body, resp.SecretKey, resp.CipherSuite, channel)
	if err != nil {
		logger.WithContext(ctx).Errorf("[decode request] decrypt error: %v", err)
		return "", errors.ChannelAcquireDecryptErr
	}

	return logs, nil
}
