/**
* Author: <PERSON>
* Email: wudong<PERSON>@rongma.com
* Date: 2023/10/9
* Time: 12:05
* Software: GoLand
 */

package middleware

import (
	"strings"
	
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	
	"rongma.com/srms/modules/consts"
)

func Cors(c *gin.Context) {
	//  标记请求的请求ID
	if c.Request.Header.Get(consts.HeaderXRequestID) == "" {
		requestId := strings.ReplaceAll(uuid.New().String(), "-", "")
		c.Set(consts.HeaderXRequestID, requestId)
		c.Header(consts.HeaderXRequestID, requestId)
	}
	
	c.Next()
}
