package middleware

import (
	"encoding/json"
	"fmt"
	"strconv"
	
	"rm.git/client_api/rm_common_libs.git/v2/common/clients/actlogger/logstruct"
	"rm.git/client_api/rm_common_libs.git/v2/common/misc"
	
	"rongma.com/srms/modules/client"
	
	"github.com/gin-gonic/gin"
	"rm.git/client_api/rm_common_libs.git/v2/common/clients/actlogger"
	"rm.git/client_api/rm_common_libs.git/v2/library/utils"
)

type TokenBucketConfig struct {
	tokenBucketNew *utils.TokenBucket
	actLogger      *actlogger.Client
}

func NewTokenBucket(actlogger *actlogger.Client) *TokenBucketConfig {
	return &TokenBucketConfig{
		tokenBucketNew: utils.NewTokenBucket(conf.TokenBucket.Rate, conf.TokenBucket.Capacity),
		actLogger:      actlogger,
	}
}

// TokenBucket 令牌桶
func (t *TokenBucketConfig) TokenBucket(ctx *gin.Context) {
	if !t.tokenBucketNew.TryConsume() {
		//http.ResponseEncrypt(c, fmt.Errorf("请求过于频繁，请稍后再试"), "", nil, t.actLogger, "", common.GetClientIp(c), "", "")
		response := misc.NewResponse(fmt.Errorf("请求过于频繁，请稍后再试")).SetSession("")
		headerJson, _ := json.Marshal(ctx.Request.Header)
		
		client.ActLogger.Error(conf.Service.Name, ctx, logstruct.Error{
			UrlPach:   ctx.Request.RequestURI,
			Method:    ctx.Request.Method,
			Header:    string(headerJson),
			ErrorCode: strconv.Itoa(response.Error),
			Request:   "",
			Message:   response.Message,
		}, "", "", "", "", conf.ActLogger.ActlogIndexName, 1)
		
		ctx.Abort()
		return
	}
}
