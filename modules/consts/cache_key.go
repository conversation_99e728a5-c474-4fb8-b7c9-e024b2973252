/**
* Author: Jason
* Email: wudong<PERSON>@rongma.com
* Date: 2023/6/30
* Time: 16:03
* Software: GoLand
 */

package consts

import "fmt"

func GetFileLevelCacheKey(md5 string) string {
	// todo 注意file_level_md5_%s在文件等级管理中会统一进行清理操作，修改时需同步
	return fmt.Sprintf("file_level_md5_%s", md5)
}

func GetChannelCacheKey(channel string) string {
	// return fmt.Sprintf("acquire_channel_%s", channel)
	return fmt.Sprintf("channel_cache_%s", channel)
}

// GetMd5RelationSha1Key 记录样本查询时md5与sha1的对应值，用于后面upload时补充sha1数据
func GetMd5RelationSha1Key(md5 string) string {
	return fmt.Sprintf("srms_md5_relation_sha1_%s", md5)
}

func GetCustomLevel(orgName, hash string) string {
	return fmt.Sprintf("srms:org:%s:custom:level:hash:%s", orgName, hash)
}

// GetFileExistCacheKey Md5是否存在
func GetFileExistCacheKey(md5 string) string {
	return fmt.Sprintf("file_exist_md5_%s", md5)
}

func GetPartnerKey(partner string) string {
	return fmt.Sprintf("partner_rule_%s", partner)
}

func GetClientThresholdKey(clientId string) string {
	return fmt.Sprintf("client_threshold_%s", clientId)
}
