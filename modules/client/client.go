package client

import (
	"context"
	"fmt"
	"os"

	"google.golang.org/grpc"
	"rm.git/client_api/rm_common_libs.git/v2/common/brokers/writer"
	"rm.git/client_api/rm_common_libs.git/v2/common/clients"
	"rm.git/client_api/rm_common_libs.git/v2/common/clients/actlogger"
	"rm.git/client_api/rm_common_libs.git/v2/common/protobuf/portal"
	rmCommonInterfaces "rm.git/client_api/rm_common_libs.git/v2/interfaces"
	rmgroup "rm.git/cloud_api/rm_common_protos.git/proto_go/rmgroup/v1"

	"rongma.com/srms/config"
	"rongma.com/srms/rmgrpc/cloudquery"
	"rongma.com/srms/rmgrpc/rmmd5"
)

var (
	conf = config.Config()
)

var (
	Redis                rmCommonInterfaces.RedisClient
	portalConn           *grpc.ClientConn
	Portal               portal.PortalServiceClient
	rmMd5Conn            *grpc.ClientConn
	RmMd5                rmmd5.GetMd5ServiceClient
	cloudQueryConn       *grpc.ClientConn
	CloudQuery           cloudquery.DoQueryClient
	ActLogger            *actlogger.Client
	MongoSrms            *clients.MongoDB
	MongoEngines         *clients.MongoDB
	Minio                *clients.Minio
	MinioSrmsShardBucket string
	MongodbInstruction   *clients.MongoDB
	GroupClient          rmgroup.RmGroupServiceClient
	groupConn            *grpc.ClientConn

	KafkaWriter *writer.Kafka
)

//nolint:funlen
func Init() {
	var err error

	if Redis, err = clients.NewRedis(conf.Redis); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	}

	if MongoSrms, err = clients.NewMongodb(conf.Mongodb, config.MongoDBSrms); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	}
	// 只有奇安信环境跟其他不一样，临时方案
	// 2024-04-01 21:13:00
	if conf.Service.CcsName == "qax-private" {
		config.MongoDBEngines = "engines01"
	}

	// minio
	if Minio, err = clients.NewMinio(conf.Minio); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	} else {
		if MinioSrmsShardBucket, err = Minio.GetBucket(config.MinioSrmsShardBucket); err != nil {
			fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
			os.Exit(-1)
		}
	}

	if MongoEngines, err = clients.NewMongodb(conf.MongodbEngines, config.MongoDBEngines); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	}

	// grpc file_md5
	if rmMd5Conn, err = clients.NewGrpc(conf.Certs, conf.GrpcFile, "rm_self"); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	}
	RmMd5 = rmmd5.NewGetMd5ServiceClient(rmMd5Conn)

	// grpc cloudquery
	if cloudQueryConn, err = clients.NewGrpc(conf.Certs, conf.GrpcEngineQuery, "rm_self"); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	}
	CloudQuery = cloudquery.NewDoQueryClient(cloudQueryConn)

	// grpc portal
	if portalConn, err = clients.NewGrpc(conf.Certs, conf.GrpcPortal, "rm_self"); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	}
	Portal = portal.NewPortalServiceClient(portalConn)

	if ActLogger, err = actlogger.NewActLogger(conf.ActLogger.IsDebug); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
	}
	if MongodbInstruction, err = clients.NewMongodb(conf.Mongodb, config.MongodbInstruction); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	}
	kafkaClient := clients.NewKafka(conf.Kafka)
	kafkaClient.SetTopic(config.KafkaReadScanTaskResultsTopic)
	if KafkaWriter, err = kafkaClient.GetWriter(); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	}
	if groupConn, err = clients.NewGrpc(conf.Certs, conf.GrpcGroup, "rm_self"); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	} else {
		GroupClient = rmgroup.NewRmGroupServiceClient(groupConn)
	}

}

func Close() {
	_ = Redis.Close()
	_ = portalConn.Close()
	_ = rmMd5Conn.Close()
	_ = cloudQueryConn.Close()
	_ = groupConn.Close()
	_ = KafkaWriter.Close()
	_ = MongodbInstruction.Client.Disconnect(context.Background())
	_ = MongoSrms.Client.Disconnect(context.Background())
	_ = MongoEngines.Client.Disconnect(context.Background())
}
